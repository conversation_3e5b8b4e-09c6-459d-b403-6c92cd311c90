syntax = "proto2";

package pb;

option go_package = "kairo_paradise_server/services/pb;pb";
message Vector2 {
    optional float x = 1;
    optional float y = 2;
}

message Vector3 {
    optional float x = 1;
    optional float y = 2;
    optional float z = 3;
}

enum EGender {
    EGender_Invalid = 0;
    EGender_Male = 1;
    EGender_Female = 2;
    EGender_Other = 4;
    EGender_MaleAndFemale = 3;
    EGender_All = 7;
}

enum EAttr {
    EAttr_Invalid = 0;
    EAttr_KitchenRecoverHungry = 1;
    EAttr_KitchenRecoverHungryRate = 2;
    EAttr_KitchenRecoverTired = 3;
    EAttr_KitchenRecoverTiredRate = 4;
    EAttr_KitchenRecoverMood = 5;
    EAttr_KitchenRecoverMoodRate = 6;
    EAttr_HomeBuildingTired = 7;
    EAttr_HeroPowerRate = 8;
    EAttr_HeroEatRate = 9;
    EAttr_HeroProdaceTimeRate = 10;
    EAttr_HeroSleepRate = 11;
    EAttr_ExploreCostRate = 12;
    EAttr_HeroExpAddRate = 13;
}

enum EHelpUIId {
    EHelpUIId_invalid = 0;
    EHelpUIId_Test = 2;
}

enum ESystemId {
    ESystemId_Invalid = 0;
    ESystemId_Base = 1;
    ESystemId_Role = 2;
    ESystemId_RoleReName = 2001;
    ESystemId_Login = 3;
    ESystemId_Lobby = 4;
    ESystemId_GM = 5;
    ESystemId_Bag = 6;
    ESystemId_Condition = 7;
    ESystemId_Task = 8;
    ESystemId_Hero = 10;
    ESystemId_DataCache = 11;
    ESystemId_SystemOpen = 12;
    ESystemId_TimeSys = 13;
    ESystemId_AI = 14;
    ESystemId_Attr = 15;
    ESystemId_HudRightBtn = 16;
    ESystemId_Mail = 17;
    ESystemId_MainCity = 18;
    ESystemId_SDK = 19;
    ESystemId_BaseRank = 23;
    ESystemId_StoryPlot = 25;
    ESystemId_Shop = 26;
    ESystemId_Tag = 27;
    ESystemId_MainHudTips = 28;
    ESystemId_Guide = 29;
    ESystemId_GameCenter = 30;
    ESystemId_RecommendGame = 31;
}

enum EConfirmRepeatType {
    EConfirmRepeatType_Forever = 0;
    EConfirmRepeatType_OnceToday = 1;
    EConfirmRepeatType_LoginTime = 2;
    EConfirmRepeatType_Once = 3;
}

enum EItem {
    EItem_Money = 0;
    EItem_Box = 1;
    EItem_Building = 2;
    EItem_HeadIcon = 3;
    EItem_Title = 4;
    EItem_Coupon = 9;
}

enum EResourceAddType {
    EResourceAddType_None = 0;
    EResourceAddType_FinishMainTask = 1;
    EResourceAddType_FinishMainTaskChapter = 2;
    EResourceAddType_SystemOpen = 3;
    EResourceAddType_System = 4;
    EResourceAddType_MapEventShop = 5;
    EResourceAddType_SystemMail = 6;
    EResourceAddType_NewPlayerMail = 7;
    EResourceAddType_WeeklyRankAward = 8;
    EResourceAddType_ConstShop = 9;
    EResourceAddType_GM = 12;
    EResourceAddType_TreasureBox = 13;
}

enum EResourceSubType {
    EResourceSubType_None = 0;
    EResourceSubType_HeroUpgrade = 1;
    EResourceSubType_HeroStar = 2;
    EResourceSubType_MapEventShop = 3;
    EResourceSubType_ConstShop = 4;
}

enum EConditionType {
    EConditionType_None = 0;
    EConditionType_Level = 1;
    EConditionType_SystemOpen = 2;
    EConditionType_GainItem = 3;
    EConditionType_FinishTask = 4;
    EConditionType_CostItem = 5;
    EConditionType_GainNewItem = 6;
    EConditionType_HeroUpgradeCount = 7;
    EConditionType_MainCityShopEvent = 8;
    EConditionType_HourOfDay12 = 9;
    EConditionType_HourOfDay24 = 10;
    EConditionType_WeeklyRankOpen = 11;
    EConditionType_TagIdsTotal = 12;
    EConditionType_TagIds = 13;
    EConditionType_CreateRoleDay = 14;
    EConditionType_TriggerGuideGroup = 15;
    EConditionType_DoneGuideGroup = 16;
    EConditionType_TotalPower = 17;
    EConditionType_HeroPower = 18;
}

enum ETaskStatus {
    ETaskStatus_None = 0;
    ETaskStatus_NotComplete = 1;
    ETaskStatus_Complete = 2;
}

enum EQuality {
    EQuality_None = 0;
    EQuality_White = 1;
    EQuality_Green = 2;
    EQuality_Blue = 3;
    EQuality_Purple = 4;
    EQuality_Orange = 5;
    EQuality_Red = 6;
    EQuality_Pink = 7;
}

enum EHeroGain {
    EHeroGain_None = 0;
    EHeroGain_SysGive = 1;
    EHeroGain_HeroCard = 2;
    EHeroGain_ChipsMerge = 3;
    EHeroGain_GM = 4;
    EHeroGain_Recruit = 5;
}

enum EHeroAttr {
    EHeroAttr_Invalid = 0;
    EHeroAttr_Power = 1;
    EHeroAttr_HumanTimeMS = 2;
    EHeroAttr_HumanTime = 3;
    EHeroAttr_TotalPurification = 4;
    EHeroAttr_BasePurification = 5;
    EHeroAttr_PurificationAddition = 6;
    EHeroAttr_PurificationFix = 7;
    EHeroAttr_Mind = 8;
    EHeroAttr_Charm = 9;
    EHeroAttr_Body = 10;
    EHeroAttr_Vigor = 11;
    EHeroAttr_Knowledge = 12;
    EHeroAttr_CriticalRate = 13;
    EHeroAttr_CriticalDamage = 14;
    EHeroAttr_ChastityAddition = 15;
    EHeroAttr_TemperanceAddition = 16;
    EHeroAttr_CharityAddition = 17;
    EHeroAttr_HopeAddition = 18;
    EHeroAttr_FortitudeAddition = 19;
    EHeroAttr_KindnessAddition = 20;
    EHeroAttr_JusticeAddition = 21;
    EHeroAttr_SkillFactor = 22;
    EHeroAttr_DamageAddition = 23;
    EHeroAttr_DamageReduction = 24;
    EHeroAttr_RestrainAddition = 25;
    EHeroAttr_ChastityInjured = 26;
    EHeroAttr_TemperanceInjured = 27;
    EHeroAttr_CharityInjured = 28;
    EHeroAttr_HopeInjured = 29;
    EHeroAttr_FortitudeInjured = 30;
    EHeroAttr_KindnessInjured = 31;
    EHeroAttr_JusticeInjured = 32;
    EHeroAttr_ChastityReduction = 33;
    EHeroAttr_TemperanceReduction = 34;
    EHeroAttr_CharityReduction = 35;
    EHeroAttr_HopeReduction = 36;
    EHeroAttr_FortitudeReduction = 37;
    EHeroAttr_KindnessReduction = 38;
    EHeroAttr_JusticeReduction = 39;
    EHeroAttr_StunRate = 40;
    EHeroAttr_MultiHitRate = 41;
    EHeroAttr_SkillPowerFactor = 42;
    EHeroAttr_GrownAttrPurificationFactor = 43;
}

enum EGainItemEffect {
    EGainItemEffect_None = 0;
    EGainItemEffect_Popup = 1;
    EGainItemEffect_IconFly = 2;
    EGainItemEffect_IconFlyMulti = 3;
    EGainItemEffect_IconFlyTouch = 4;
}

enum ERankType {
    ERankType_None = 0;
    ERankType_CivilProsperity = 1;
    ERankType_LineUpPower = 2;
    ERankType_TotalPower = 3;
    ERankType_BossCopper = 9981;
    ERankType_BossSilver = 9982;
    ERankType_BossGold = 9983;
    ERankType_MiniGameBegin = 1000;
}

enum EGoto {
    EGoto_Invalid = 0;
    EGoto_UI = 1;
    EGoto_Scene = 2;
    EGoto_MainBtn = 3;
    EGoto_Web = 4;
    EGoto_TextDes = 5;
    EGoto_MainPlayerView = 6;
    EGoto_GuideEffect = 7;
    EGoto_DelayOpenUI = 8;
}

enum EMailStatus {
    EMailStatus_None = 0;
    EMailStatus_Unread = 1;
    EMailStatus_Read = 2;
    EMailStatus_Gain = 3;
}

enum EAINodeType {
    EAINodeType_None = 0;
    EAINodeType_Execute = 1;
    EAINodeType_Loop = 2;
    EAINodeType_Select = 3;
}

enum EAIConditionType {
    EAIConditionType_None = 0;
    EAIConditionType_And = 1;
    EAIConditionType_Or = 2;
    EAIConditionType_CheckCurAITime = 3;
    EAIConditionType_CheckVariable = 4;
    EAIConditionType_CheckLastAITime = 5;
    EAIConditionType_CheckNextAITime = 6;
    EAIConditionType_CheckBuildType = 7;
}

enum EAIActionType {
    EAIActionType_None = 0;
    EAIActionType_Delay = 1;
    EAIActionType_SetVariable = 2;
    EAIActionType_ExecuteNode = 3;
    EAIActionType_Finish = 4;
}

enum EAwardType {
    EAwardType_None = 0;
    EAwardType_Fixed = 1;
    EAwardType_Weight = 2;
    EAwardType_Random = 3;
}

enum EAwardGroupType {
    EAwardGroupType_None = 0;
    EAwardGroupType_Random = 1;
    EAwardGroupType_Weight = 2;
}

enum EStoryPlotConditionType {
    EStoryPlotConditionType_None = 0;
    EStoryPlotConditionType_EventDone = 1;
}

enum EStoryPlotInstructType {
    EStoryPlotInstructType_None = 0;
    EStoryPlotInstructType_Wait = 3;
    EStoryPlotInstructType_AddNpc = 4;
    EStoryPlotInstructType_RemoveNpc = 5;
    EStoryPlotInstructType_NpcMove = 6;
    EStoryPlotInstructType_NpcBubble = 7;
    EStoryPlotInstructType_NpcAction = 8;
    EStoryPlotInstructType_NpcEvent = 9;
    EStoryPlotInstructType_AddPlayer = 10;
    EStoryPlotInstructType_PlayerMove = 11;
    EStoryPlotInstructType_PlayerAction = 12;
    EStoryPlotInstructType_PlayerBubble = 13;
    EStoryPlotInstructType_FocusPlayer = 14;
    EStoryPlotInstructType_FocusNpc = 15;
    EStoryPlotInstructType_FocusPoint = 16;
    EStoryPlotInstructType_ReleaseFocus = 17;
    EStoryPlotInstructType_Finish = 18;
    EStoryPlotInstructType_HideUI = 19;
    EStoryPlotInstructType_ShowUI = 20;
    EStoryPlotInstructType_EventBubble = 21;
    EStoryPlotInstructType_NpcDialog = 22;
    EStoryPlotInstructType_HeroDialog = 23;
    EStoryPlotInstructType_Black = 24;
    EStoryPlotInstructType_GuidePointer = 25;
    EStoryPlotInstructType_ChangeBGM = 26;
    EStoryPlotInstructType_NpcAddEffect = 27;
    EStoryPlotInstructType_PlaySound = 28;
    EStoryPlotInstructType_AddNpcActionFlag = 29;
    EStoryPlotInstructType_RemoveNpcActionFlag = 30;
    EStoryPlotInstructType_AddPlayerActionFlag = 31;
    EStoryPlotInstructType_RemovePlayerActionFlag = 32;
    EStoryPlotInstructType_AddScenePicture = 33;
    EStoryPlotInstructType_RemoveSceneStaticActor = 34;
    EStoryPlotInstructType_AddSceneEffect = 35;
    EStoryPlotInstructType_ChangeSceneEffect = 36;
    EStoryPlotInstructType_RemoveNpcEffect = 37;
    EStoryPlotInstructType_AddCelebrateView = 38;
    EStoryPlotInstructType_PlayVideo = 39;
    EStoryPlotInstructType_AddHero = 40;
    EStoryPlotInstructType_RemoveHero = 41;
    EStoryPlotInstructType_HeroMove = 42;
    EStoryPlotInstructType_HeroBubble = 43;
    EStoryPlotInstructType_HeroAction = 44;
    EStoryPlotInstructType_FocusHero = 45;
    EStoryPlotInstructType_AddHeroEffect = 46;
    EStoryPlotInstructType_RemoveHeroEffect = 47;
    EStoryPlotInstructType_AddHeroActionFlag = 48;
    EStoryPlotInstructType_RemoveHeroActionFlag = 49;
    EStoryPlotInstructType_LoadUIViewContent = 50;
    EStoryPlotInstructType_UIViewSetHeroSpine = 51;
    EStoryPlotInstructType_UIViewAddBubble = 52;
    EStoryPlotInstructType_WaitPlayerInput = 53;
    EStoryPlotInstructType_StoryDialog = 54;
    EStoryPlotInstructType_UIViewPlayTransition = 55;
    EStoryPlotInstructType_OpenUIAndWaitClose = 56;
    EStoryPlotInstructType_ReportShushu = 57;
}

enum EShopBuyLimit {
    EShopBuyLimit_None = 0;
    EShopBuyLimit_Daily = 1;
    EShopBuyLimit_Weekly = 2;
    EShopBuyLimit_Permanent = 3;
}

enum EShopType {
    EShopType_PropShop = 0;
    EShopType_ConstShop = 1;
}

enum EStoryPlotType {
    EStoryPlotType_None = 0;
    EStoryPlotType_MainScene = 1;
    EStoryPlotType_ExploreScene = 2;
    EStoryPlotType_StoryScene = 3;
    EStoryPlotType_StoryUIView = 4;
}

enum EStoryPlotInstructConditionType {
    EStoryPlotInstructConditionType_None = 0;
    EStoryPlotInstructConditionType_SlimeTaskChapterFinish = 1;
    EStoryPlotInstructConditionType_HomeBuildingLevel = 2;
    EStoryPlotInstructConditionType_HasHeroId = 3;
}

enum EMUnit {
    EMUnit_Root = 0;
    EMUnit_Map = 1;
    EMUnit_Building = 10;
    EMUnit_WorkSpot = 11;
    EMUnit_Event = 12;
    EMUnit_Special = 13;
    EMUnit_Cloud = 14;
    EMUnit_Staff = 15;
    EMUnit_Furnture = 16;
}

enum ETileBlock {
    ETileBlock_Invalid = 0;
    ETileBlock_Data = 1;
    ETileBlock_BuildingArea = 2;
    ETileBlock_Cloud = 3;
    ETileBlock_EntityTile = 4;
    ETileBlock_LotteryDup = 5;
}

enum EMapLogicLayer {
    EMapLogicLayer_Invalid = 0;
    EMapLogicLayer_Data = 1;
    EMapLogicLayer_Building = 2;
    EMapLogicLayer_Clouds = 3;
    EMapLogicLayer_MUnit = 4;
}

enum EMapType {
    EMapType_Invalid = 0;
    EMapType_Scene = 1;
    EMapType_LotteryDup = 2;
}

enum EMapLayer {
    EMapLayer_None = 0;
    EMapLayer_BuildingFloorLayer = 1;
    EMapLayer_StaffDownLayer = 2;
    EMapLayer_HeroLayer = 3;
    EMapLayer_StaffUpLayer = 4;
    EMapLayer_BuildingRoofLayer = 5;
    EMapLayer_BuildingBarLayer = 6;
    EMapLayer_BuildingRoofLayerUp = 7;
    EMapLayer_CloudTopLayer = 8;
}

enum EGuide {
    EGuide_None = 0;
    EGuide_GuidePointer = 6;
    EGuide_GuideBuilding = 26;
    EGuide_GuideCityEvent = 27;
}

enum EAttrFormatType {
    EAttrFormatType_None = 0;
    EAttrFormatType_Value = 1;
    EAttrFormatType_Percent = 2;
    EAttrFormatType_Time = 3;
}

enum ERedPoint {
    ERedPoint_invalid = 0;
    ERedPoint_AlawaysTrue = 1;
    ERedPoint_MailSys = 2;
}

enum ETileBlockWorkType {
    ETileBlockWorkType_Seat = 0;
    ETileBlockWorkType_Queue = 1;
    ETileBlockWorkType_Gate = 2;
    ETileBlockWorkType_Rent = 31;
}

enum ECondTriggerType {
    ECondTriggerType_None = 0;
    ECondTriggerType_ModifyTime = 1;
}

enum EActorDirection {
    EActorDirection_None = 0;
    EActorDirection_Left = 1;
    EActorDirection_Right = 2;
}

message AttrData {
    optional EAttr Attr = 1;
    optional int64 Value = 2;
}

message ItemData {
    optional int32 Id = 1;
    optional int32 Count = 2;
}

message HeroAttr {
    optional EHeroAttr Attr = 1;
    optional int64 Value = 2;
}

message RandomItemData {
    optional int32 Id = 1;
    optional int64 Count = 2;
    optional int32 Prop = 3;
}

message AwardGroupData {
    optional int32 Id = 1;
    optional int32 Count = 2;
    optional int32 Prop = 3;
    optional int32 Rate = 4;
}

message ConditionWithDesc {
    optional int32 ConditionId = 1;
    optional string Desc = 2;
}

message ItemProperty {
    optional int32 ItemId = 1;
    optional int32 Prop = 2;
}

message GridPosition {
    optional int32 x = 1;
    optional int32 y = 2;
}

message GuideDetailTips {
    optional string Tips = 1;
    optional string Icon = 2;
    optional int32 OffsetX = 3;
    optional int32 OffsetY = 4;
}

message CountPropData {
    optional int32 Count = 1;
    optional int32 Prop = 2;
}

message _v_int32 {
    repeated int32 a = 1;
}
// sheet: AIActionConfig
message listAIActionConfig
{
    message AIActionConfig
    {
        optional int32 Id = 2;
    }
    repeated AIActionConfig list = 1;
}


// sheet: AIConditionConfig
message listAIConditionConfig
{
    message AIConditionConfig
    {
        optional int32 Id = 2;
        optional EAIConditionType Type = 3;
    }
    repeated AIConditionConfig list = 1;
}


// sheet: AINodeConfig
message listAINodeConfig
{
    message AINodeConfig
    {
        optional int32 Id = 2;
    }
    repeated AINodeConfig list = 1;
}


// sheet: AvatarConfig
message listAvatarConfig
{
    message AvatarConfig
    {
        optional int32 Id = 2;
    }
    repeated AvatarConfig list = 1;
}


// sheet: AwardConfig
message listAwardConfig
{
    message AwardConfig
    {
        optional int32 Id = 2;
        optional EAwardType Type = 4;
        repeated AwardGroupData Award = 5;
        optional int32 Count = 6;
    }
    repeated AwardConfig list = 1;
}


// sheet: AwardGroupConfig
message listAwardGroupConfig
{
    message AwardGroupConfig
    {
        optional int32 Id = 2;
        optional int32 GroupId = 4;
        optional EAwardGroupType Type = 5;
        optional int32 ItemId = 6;
        optional int64 Count = 8;
        optional int32 Value = 9;
    }
    repeated AwardGroupConfig list = 1;
}


// sheet: ConditionConfig
message listConditionConfig
{
    message ConditionConfig
    {
        optional int32 Id = 2;
        optional EConditionType Type = 5;
        optional int32 Target = 6;
        optional int32 Param1 = 7;
        optional int32 Param2 = 8;
        optional int32 Param3 = 9;
        optional int32 Param4 = 10;
        optional int32 CondVersion = 11;
    }
    repeated ConditionConfig list = 1;
}


// sheet: ConditionTriggerConfig
message listConditionTriggerConfig
{
    message ConditionTriggerConfig
    {
        optional int32 Id = 2;
        optional EConditionType CondType = 4;
        optional int32 CondTarget = 5;
        repeated int32 CondParams = 6;
        optional int32 CondVersion = 7;
        optional ECondTriggerType TiggerType = 8;
        repeated string TriggerParams = 9;
    }
    repeated ConditionTriggerConfig list = 1;
}


// sheet: ConstConfig
message listConstConfig
{
    message ConstConfig
    {
        optional int32 Id = 2;
        optional int32 VInt = 4;
        optional int64 VLong = 5;
        repeated int32 VIntList = 6;
        repeated _v_int32 VIntLList = 7;
        optional string VString = 8;
    }
    repeated ConstConfig list = 1;
}


// sheet: ConstShopGoodsConfig
message listConstShopGoodsConfig
{
    message ConstShopGoodsConfig
    {
        optional int32 Id = 2;
        optional ItemData ItemData = 6;
        optional ItemData CostItem = 7;
        optional EShopBuyLimit LimitType = 8;
        optional int32 LimitCount = 9;
        optional EConditionType CondType = 10;
        optional int32 CondTarget = 11;
        repeated int32 CondParams = 12;
        optional int32 CondVersion = 13;
    }
    repeated ConstShopGoodsConfig list = 1;
}


// sheet: GainItemConfig
message listGainItemConfig
{
    message GainItemConfig
    {
        optional int32 Id = 2;
        optional EGainItemEffect EffectType = 4;
    }
    repeated GainItemConfig list = 1;
}


// sheet: GuideConfig
message listGuideConfig
{
    message GuideConfig
    {
        optional int32 Id = 2;
        optional int32 GroupId = 3;
        optional EGuide Type = 6;
        repeated string Params = 9;
    }
    repeated GuideConfig list = 1;
}


// sheet: GuideGroupConfig
message listGuideGroupConfig
{
    message GuideGroupConfig
    {
        optional int32 Id = 2;
        optional bool RertyOnFailure = 4;
        optional EConditionType CondType = 6;
        optional int32 CondTarget = 7;
        repeated int32 CondParams = 8;
        optional int32 CondVersion = 9;
    }
    repeated GuideGroupConfig list = 1;
}


// sheet: HeadPortraitConfig
message listHeadPortraitConfig
{
    message HeadPortraitConfig
    {
        optional int32 Id = 2;
        optional bool IsDefault = 4;
        optional EConditionType CondType = 7;
        optional int32 CondTarget = 8;
        repeated int32 CondParams = 9;
        optional int32 CondVersion = 10;
    }
    repeated HeadPortraitConfig list = 1;
}


// sheet: HeroConfig
message listHeroConfig
{
    message HeroConfig
    {
        optional int32 Id = 2;
        optional EQuality Quality = 6;
        optional int32 Star = 7;
        optional ItemData NeedChips = 8;
        repeated HeroAttr BaseAttr = 9;
        optional int32 InitEnergy = 10;
        optional int32 LevelGroup = 11;
        optional int32 StarGroupId = 12;
        optional bool Duplicate = 13;
    }
    repeated HeroConfig list = 1;
}


// sheet: HeroLevelConfig
message listHeroLevelConfig
{
    message HeroLevelConfig
    {
        optional int32 Id = 2;
        optional int32 Level = 3;
        optional int32 GroupId = 4;
        repeated HeroAttr Attrs = 5;
        optional ItemData CostItem = 6;
    }
    repeated HeroLevelConfig list = 1;
}


// sheet: HeroQualityConfig
message listHeroQualityConfig
{
    message HeroQualityConfig
    {
        optional int32 Id = 2;
        optional int32 MaxStar = 4;
    }
    repeated HeroQualityConfig list = 1;
}


// sheet: HeroStarConfig
message listHeroStarConfig
{
    message HeroStarConfig
    {
        optional int32 Id = 2;
        optional int32 GroupId = 3;
        optional int32 StarLevel = 4;
        optional int32 CostChips = 5;
        optional ItemData CostItem = 6;
        repeated HeroAttr Attrs = 7;
    }
    repeated HeroStarConfig list = 1;
}


// sheet: ItemConfig
message listItemConfig
{
    message ItemConfig
    {
        optional int32 Id = 2;
        optional string Name = 3;
        optional EItem Type = 4;
        optional int32 SubType = 5;
        optional int32 PersonalDailyLimit = 6;
        optional int32 GlobalDailyLimit = 7;
        optional int32 GlobalWeekLimit = 8;
        optional int32 GlobalMonthLimit = 9;
        optional int64 StartTime = 10;
        optional int64 ExpireTime = 11;
        optional int32 ValidHours = 12;
        optional int32 Overlay = 13;
        optional int32 Bind = 14;
        optional string Icon = 15;
        optional string TinyIcon = 16;
        optional string Desc = 17;
    }
    repeated ItemConfig list = 1;
}


// sheet: LevelConfig
message listLevelConfig
{
    message LevelConfig
    {
        optional int32 Id = 2;
        optional int32 Prosperity = 3;
    }
    repeated LevelConfig list = 1;
}


// sheet: MailConfig
message listMailConfig
{
    message MailConfig
    {
        optional int32 Id = 2;
        optional string Title = 4;
        optional string Content = 5;
        repeated ItemData Attachments = 6;
        optional string Sender = 7;
    }
    repeated MailConfig list = 1;
}


// sheet: MainCityScaleConfig
message listMainCityScaleConfig
{
    message MainCityScaleConfig
    {
        optional int32 Id = 2;
        optional int32 Condition = 6;
    }
    repeated MainCityScaleConfig list = 1;
}


// sheet: MainTaskChapterConfig
message listMainTaskChapterConfig
{
    message MainTaskChapterConfig
    {
        optional int32 Id = 2;
        optional string Name = 3;
        optional int32 Award = 6;
    }
    repeated MainTaskChapterConfig list = 1;
}


// sheet: MainTaskConfig
message listMainTaskConfig
{
    message MainTaskConfig
    {
        optional int32 Id = 2;
        optional string Name = 3;
        repeated string NameArgs = 4;
        optional int32 ChapterId = 7;
        optional int32 PreId = 9;
        optional bool DefaultAccept = 10;
        optional EConditionType CondType = 11;
        optional int32 CondTarget = 12;
        repeated int32 CondParams = 13;
        optional int32 CondVersion = 14;
        optional int32 Award = 15;
        repeated int32 TagIds = 18;
        optional bool DefaultComplete = 19;
    }
    repeated MainTaskConfig list = 1;
}


// sheet: MapConfig
message listMapConfig
{
    message MapConfig
    {
        optional int32 Id = 2;
        optional int32 TileMapId = 3;
    }
    repeated MapConfig list = 1;
}


// sheet: MiniGameConfig
message listMiniGameConfig
{
    message MiniGameConfig
    {
        optional int32 Id = 2;
        optional string Identity = 3;
        optional string Name = 4;
        optional string Desc = 5;
    }
    repeated MiniGameConfig list = 1;
}


// sheet: MUnitConfig
message listMUnitConfig
{
    message MUnitConfig
    {
        optional int32 Id = 2;
        optional EMUnit Type = 4;
        optional int32 Width = 5;
        optional int32 Height = 6;
        optional string AssetBundle = 7;
        optional float GridSize = 8;
    }
    repeated MUnitConfig list = 1;
}


// sheet: RankConfig
message listRankConfig
{
    message RankConfig
    {
        optional int32 Id = 2;
        optional string Name = 3;
        optional int32 MinValueLimit = 4;
        optional int32 MaxQueryLimit = 5;
        optional int32 ShowRankLimit = 6;
        optional int32 MaxRankLimit = 7;
    }
    repeated RankConfig list = 1;
}


// sheet: RechargeConfig
message listRechargeConfig
{
    message RechargeConfig
    {
        optional int32 Id = 2;
        optional string Identity = 3;
    }
    repeated RechargeConfig list = 1;
}


// sheet: ShopGoodsConfig
message listShopGoodsConfig
{
    message ShopGoodsConfig
    {
        optional int32 Id = 2;
        optional int32 GroupId = 3;
        optional ItemData ItemData = 5;
        optional ItemData CostItem = 6;
        optional int32 Discount = 7;
        repeated int32 Level = 8;
        optional int32 ConditionId = 9;
        optional int32 Prop = 10;
    }
    repeated ShopGoodsConfig list = 1;
}


// sheet: ShopTabConfig
message listShopTabConfig
{
    message ShopTabConfig
    {
        optional int32 Id = 2;
        optional EShopType Type = 6;
        optional int32 GroupId = 7;
        optional int32 GoodsCount = 8;
        optional ESystemId SystemId = 9;
        repeated ItemData RefreshCost = 10;
    }
    repeated ShopTabConfig list = 1;
}


// sheet: StoryPlotConditionConfig
message listStoryPlotConditionConfig
{
    message StoryPlotConditionConfig
    {
        optional int32 Id = 2;
        optional int32 Group = 4;
        optional EStoryPlotConditionType Type = 5;
        repeated string Args = 6;
    }
    repeated StoryPlotConditionConfig list = 1;
}


// sheet: StoryPlotConfig
message listStoryPlotConfig
{
    message StoryPlotConfig
    {
        optional int32 Id = 2;
        optional int32 Group = 4;
        optional int32 PreId = 5;
        optional int32 ConditionGroup = 6;
        optional int32 InstructGroup = 7;
    }
    repeated StoryPlotConfig list = 1;
}


// sheet: StoryPlotGroupConfig
message listStoryPlotGroupConfig
{
    message StoryPlotGroupConfig
    {
        optional int32 Id = 2;
        optional EStoryPlotType Type = 4;
    }
    repeated StoryPlotGroupConfig list = 1;
}


// sheet: StoryPlotInstructsConfig
message listStoryPlotInstructsConfig
{
    message StoryPlotInstructsConfig
    {
        optional int32 Id = 2;
        optional int32 Group = 4;
        optional EStoryPlotInstructType Type = 5;
        repeated string Args = 6;
        optional int32 Wait = 7;
        optional EStoryPlotInstructConditionType Conditon = 8;
        repeated string conditionArgs = 9;
    }
    repeated StoryPlotInstructsConfig list = 1;
}


// sheet: SystemOpenConfig
message listSystemOpenConfig
{
    message SystemOpenConfig
    {
        optional int32 Id = 2;
        optional int32 ConditionId = 8;
        optional int32 ParentId = 9;
        optional int32 Version = 10;
        optional bool Disabled = 12;
    }
    repeated SystemOpenConfig list = 1;
}


// sheet: TagConfig
message listTagConfig
{
    message TagConfig
    {
        optional int32 Id = 2;
    }
    repeated TagConfig list = 1;
}


// sheet: TileMapConfig
message listTileMapConfig
{
    message TileMapConfig
    {
        optional int32 Id = 2;
        optional int32 MapType = 4;
        optional int32 Width = 5;
        optional int32 Height = 6;
        optional float GridSize = 7;
        optional string AssetBundle = 8;
        optional string PrefabName = 9;
    }
    repeated TileMapConfig list = 1;
}


// sheet: UserNameConfig
message listUserNameConfig
{
    message UserNameConfig
    {
        optional int32 Id = 2;
        optional string name = 3;
        optional int32 type = 4;
    }
    repeated UserNameConfig list = 1;
}



