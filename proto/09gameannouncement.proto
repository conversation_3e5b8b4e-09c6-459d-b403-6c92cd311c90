syntax = "proto2";
option go_package = "kairo_paradise_server/services/pb;pb";
package pb;
import "code.proto";
import "gameconfig.proto";

enum EAnnouncementType {
  EAnnouncementType_None = 0;
  EAnnouncementType_Marquee = 1;      // 跑马灯
  EAnnouncementType_LoginPopup = 2;   // 登录弹框
  EAnnouncementType_Board = 3;        // 公告板
  EAnnouncementType_Activity = 4;     // 活动公告
  EAnnouncementType_System = 5;       // 系统公告
}

enum EAnnouncementStatus {
  EAnnouncementStatus_Unread = 1;      // 未读
  EAnnouncementStatus_Read = 2;        // 已读
}

// 公告配置
message AnnouncementConfig {
  optional int32 Id = 1;
  optional EAnnouncementType Type = 2;
  optional string Title = 3;
  optional string Content = 4;
  optional int32 Priority = 5;
  optional int64 StartTime = 6;
  optional int64 EndTime = 7;
  optional int32 ShowInterval = 8;  // 显示间隔(秒)
  optional EConfirmRepeatType RepeatType = 9;  // 重复类型
  optional string JumpTarget = 10;  // 跳转目标
}

// 公告信息
message AnnouncementInfo {
  required uint32 id = 1;         // 公告ID
  required EAnnouncementType type = 2;  // 公告类型
  required string title = 3;      // 公告标题
  required string content = 4;    // 公告内容
  required int32 status = 5;      // 公告状态: 1=未读, 2=已读
  required int32 priority = 6;    // 优先级
  required int64 createdAt = 7;   // 创建时间
  required int64 startTime = 8;   // 开始时间
  required int64 endTime = 9;     // 结束时间
  optional int32 showInterval = 10; // 显示间隔(秒)
  optional string jumpTarget = 11;  // 跳转目标
}

// 公告协议
// 10210 - 10259

// 获取公告列表
// 10210
// USED
message C2SAnnouncementList {
  optional EAnnouncementType type = 1;  // 公告类型，不传则获取所有类型
}

// 公告列表返回
// 10211
// USED
message S2CAnnouncementList {
  repeated AnnouncementInfo announcements = 1;
}

// 读取公告
// 10212
// USED
message C2SAnnouncementRead {
  required uint32 announcementId = 1;
}

// 读取公告返回
// 10213
// USED
message S2CAnnouncementRead {
  required response_code code = 1;
  optional string message = 2;
  required AnnouncementInfo announcement = 3;
}

// 新公告通知
// 10214
// USED
message S2CAnnouncementNotify {
  required AnnouncementInfo announcement = 1;
}

// GM创建公告
// 10215
// USED
message C2SGMCreateAnnouncement {
  required EAnnouncementType type = 1;  // 公告类型
  required string title = 2;     // 公告标题
  required string content = 3;   // 公告内容
  required int32 priority = 4;   // 优先级
  required int64 startTime = 5;  // 开始时间戳
  required int64 endTime = 6;    // 结束时间戳
  optional int32 showInterval = 7; // 显示间隔(秒)
  optional string jumpTarget = 8;  // 跳转目标
}

// GM创建公告返回
// 10216
// USED
message S2CGMCreateAnnouncement {
  required response_code code = 1;
  optional string message = 2;
}