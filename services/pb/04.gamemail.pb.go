// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: 04.gamemail.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 邮件信息
type MailInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,req,name=id" json:"id,omitempty"`                  // 邮件ID
	Title         *string                `protobuf:"bytes,2,req,name=title" json:"title,omitempty"`             // 邮件标题
	Content       *string                `protobuf:"bytes,3,req,name=content" json:"content,omitempty"`         // 邮件内容
	Sender        *string                `protobuf:"bytes,4,req,name=sender" json:"sender,omitempty"`           // 发送者
	Status        *int32                 `protobuf:"varint,5,req,name=status" json:"status,omitempty"`          // 邮件状态: 1=未读, 2=已读, 3=已领取
	CreatedAt     *int64                 `protobuf:"varint,6,req,name=createdAt" json:"createdAt,omitempty"`    // 创建时间
	ExpireAt      *int64                 `protobuf:"varint,7,opt,name=expireAt" json:"expireAt,omitempty"`      // 过期时间
	Attachments   []*ItemData            `protobuf:"bytes,8,rep,name=attachments" json:"attachments,omitempty"` // 附件
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MailInfo) Reset() {
	*x = MailInfo{}
	mi := &file__04_gamemail_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailInfo) ProtoMessage() {}

func (x *MailInfo) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailInfo.ProtoReflect.Descriptor instead.
func (*MailInfo) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{0}
}

func (x *MailInfo) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *MailInfo) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *MailInfo) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

func (x *MailInfo) GetSender() string {
	if x != nil && x.Sender != nil {
		return *x.Sender
	}
	return ""
}

func (x *MailInfo) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *MailInfo) GetCreatedAt() int64 {
	if x != nil && x.CreatedAt != nil {
		return *x.CreatedAt
	}
	return 0
}

func (x *MailInfo) GetExpireAt() int64 {
	if x != nil && x.ExpireAt != nil {
		return *x.ExpireAt
	}
	return 0
}

func (x *MailInfo) GetAttachments() []*ItemData {
	if x != nil {
		return x.Attachments
	}
	return nil
}

// 获取邮件列表
// 10160
// USED
type C2SMailList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SMailList) Reset() {
	*x = C2SMailList{}
	mi := &file__04_gamemail_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SMailList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SMailList) ProtoMessage() {}

func (x *C2SMailList) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SMailList.ProtoReflect.Descriptor instead.
func (*C2SMailList) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{1}
}

// 邮件列表返回
// 10161
// USED
type S2CMailList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mails         []*MailInfo            `protobuf:"bytes,1,rep,name=mails" json:"mails,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CMailList) Reset() {
	*x = S2CMailList{}
	mi := &file__04_gamemail_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CMailList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CMailList) ProtoMessage() {}

func (x *S2CMailList) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CMailList.ProtoReflect.Descriptor instead.
func (*S2CMailList) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{2}
}

func (x *S2CMailList) GetMails() []*MailInfo {
	if x != nil {
		return x.Mails
	}
	return nil
}

// 读取邮件
// 10162
// USED
type C2SMailRead struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MailId        *uint64                `protobuf:"varint,1,req,name=mailId" json:"mailId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SMailRead) Reset() {
	*x = C2SMailRead{}
	mi := &file__04_gamemail_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SMailRead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SMailRead) ProtoMessage() {}

func (x *C2SMailRead) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SMailRead.ProtoReflect.Descriptor instead.
func (*C2SMailRead) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{3}
}

func (x *C2SMailRead) GetMailId() uint64 {
	if x != nil && x.MailId != nil {
		return *x.MailId
	}
	return 0
}

// 领取邮件附件
// 10164
// USED
type C2SMailClaim struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MailId        *uint64                `protobuf:"varint,1,req,name=mailId" json:"mailId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SMailClaim) Reset() {
	*x = C2SMailClaim{}
	mi := &file__04_gamemail_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SMailClaim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SMailClaim) ProtoMessage() {}

func (x *C2SMailClaim) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SMailClaim.ProtoReflect.Descriptor instead.
func (*C2SMailClaim) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{4}
}

func (x *C2SMailClaim) GetMailId() uint64 {
	if x != nil && x.MailId != nil {
		return *x.MailId
	}
	return 0
}

// 通知删除邮件（注：非RPC协议）
// 10165
// USED
type S2CMailDelete struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MailId        []uint64               `protobuf:"varint,1,rep,name=mailId" json:"mailId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CMailDelete) Reset() {
	*x = S2CMailDelete{}
	mi := &file__04_gamemail_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CMailDelete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CMailDelete) ProtoMessage() {}

func (x *S2CMailDelete) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CMailDelete.ProtoReflect.Descriptor instead.
func (*S2CMailDelete) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{5}
}

func (x *S2CMailDelete) GetMailId() []uint64 {
	if x != nil {
		return x.MailId
	}
	return nil
}

// 请求删除邮件（注：非RPC协议）
// 10166
// USED
type C2SMailDelete struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MailId        *uint64                `protobuf:"varint,1,req,name=mailId" json:"mailId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SMailDelete) Reset() {
	*x = C2SMailDelete{}
	mi := &file__04_gamemail_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SMailDelete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SMailDelete) ProtoMessage() {}

func (x *C2SMailDelete) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SMailDelete.ProtoReflect.Descriptor instead.
func (*C2SMailDelete) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{6}
}

func (x *C2SMailDelete) GetMailId() uint64 {
	if x != nil && x.MailId != nil {
		return *x.MailId
	}
	return 0
}

// 新邮件通知
// 10167
// USED
type S2CMailNotify struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mail          *MailInfo              `protobuf:"bytes,1,req,name=mail" json:"mail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CMailNotify) Reset() {
	*x = S2CMailNotify{}
	mi := &file__04_gamemail_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CMailNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CMailNotify) ProtoMessage() {}

func (x *S2CMailNotify) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CMailNotify.ProtoReflect.Descriptor instead.
func (*S2CMailNotify) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{7}
}

func (x *S2CMailNotify) GetMail() *MailInfo {
	if x != nil {
		return x.Mail
	}
	return nil
}

// 一键阅读所有邮件
// 10168
// USED
type C2SReadAllMail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SReadAllMail) Reset() {
	*x = C2SReadAllMail{}
	mi := &file__04_gamemail_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SReadAllMail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SReadAllMail) ProtoMessage() {}

func (x *C2SReadAllMail) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SReadAllMail.ProtoReflect.Descriptor instead.
func (*C2SReadAllMail) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{8}
}

// 一键领取邮件附件
// 10169
// USED
type C2SClaimMailAll struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SClaimMailAll) Reset() {
	*x = C2SClaimMailAll{}
	mi := &file__04_gamemail_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SClaimMailAll) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SClaimMailAll) ProtoMessage() {}

func (x *C2SClaimMailAll) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SClaimMailAll.ProtoReflect.Descriptor instead.
func (*C2SClaimMailAll) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{9}
}

// 一键删除所有已读已领取附件的邮件
// 10170
// USED
type C2SDelAllMail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SDelAllMail) Reset() {
	*x = C2SDelAllMail{}
	mi := &file__04_gamemail_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SDelAllMail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SDelAllMail) ProtoMessage() {}

func (x *C2SDelAllMail) ProtoReflect() protoreflect.Message {
	mi := &file__04_gamemail_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SDelAllMail.ProtoReflect.Descriptor instead.
func (*C2SDelAllMail) Descriptor() ([]byte, []int) {
	return file__04_gamemail_proto_rawDescGZIP(), []int{10}
}

var File__04_gamemail_proto protoreflect.FileDescriptor

const file__04_gamemail_proto_rawDesc = "" +
	"\n" +
	"\x1104.gamemail.proto\x12\x02pb\x1a\n" +
	"code.proto\x1a\x10gameconfig.proto\"\xe4\x01\n" +
	"\bMailInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x02(\x04R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x02(\tR\x05title\x12\x18\n" +
	"\acontent\x18\x03 \x02(\tR\acontent\x12\x16\n" +
	"\x06sender\x18\x04 \x02(\tR\x06sender\x12\x16\n" +
	"\x06status\x18\x05 \x02(\x05R\x06status\x12\x1c\n" +
	"\tcreatedAt\x18\x06 \x02(\x03R\tcreatedAt\x12\x1a\n" +
	"\bexpireAt\x18\a \x01(\x03R\bexpireAt\x12.\n" +
	"\vattachments\x18\b \x03(\v2\f.pb.ItemDataR\vattachments\"\r\n" +
	"\vC2SMailList\"1\n" +
	"\vS2CMailList\x12\"\n" +
	"\x05mails\x18\x01 \x03(\v2\f.pb.MailInfoR\x05mails\"%\n" +
	"\vC2SMailRead\x12\x16\n" +
	"\x06mailId\x18\x01 \x02(\x04R\x06mailId\"&\n" +
	"\fC2SMailClaim\x12\x16\n" +
	"\x06mailId\x18\x01 \x02(\x04R\x06mailId\"'\n" +
	"\rS2CMailDelete\x12\x16\n" +
	"\x06mailId\x18\x01 \x03(\x04R\x06mailId\"'\n" +
	"\rC2SMailDelete\x12\x16\n" +
	"\x06mailId\x18\x01 \x02(\x04R\x06mailId\"1\n" +
	"\rS2CMailNotify\x12 \n" +
	"\x04mail\x18\x01 \x02(\v2\f.pb.MailInfoR\x04mail\"\x10\n" +
	"\x0eC2SReadAllMail\"\x11\n" +
	"\x0fC2SClaimMailAll\"\x0f\n" +
	"\rC2SDelAllMailB&Z$kairo_paradise_server/services/pb;pb"

var (
	file__04_gamemail_proto_rawDescOnce sync.Once
	file__04_gamemail_proto_rawDescData []byte
)

func file__04_gamemail_proto_rawDescGZIP() []byte {
	file__04_gamemail_proto_rawDescOnce.Do(func() {
		file__04_gamemail_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file__04_gamemail_proto_rawDesc), len(file__04_gamemail_proto_rawDesc)))
	})
	return file__04_gamemail_proto_rawDescData
}

var file__04_gamemail_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file__04_gamemail_proto_goTypes = []any{
	(*MailInfo)(nil),        // 0: pb.MailInfo
	(*C2SMailList)(nil),     // 1: pb.C2SMailList
	(*S2CMailList)(nil),     // 2: pb.S2CMailList
	(*C2SMailRead)(nil),     // 3: pb.C2SMailRead
	(*C2SMailClaim)(nil),    // 4: pb.C2SMailClaim
	(*S2CMailDelete)(nil),   // 5: pb.S2CMailDelete
	(*C2SMailDelete)(nil),   // 6: pb.C2SMailDelete
	(*S2CMailNotify)(nil),   // 7: pb.S2CMailNotify
	(*C2SReadAllMail)(nil),  // 8: pb.C2SReadAllMail
	(*C2SClaimMailAll)(nil), // 9: pb.C2SClaimMailAll
	(*C2SDelAllMail)(nil),   // 10: pb.C2SDelAllMail
	(*ItemData)(nil),        // 11: pb.ItemData
}
var file__04_gamemail_proto_depIdxs = []int32{
	11, // 0: pb.MailInfo.attachments:type_name -> pb.ItemData
	0,  // 1: pb.S2CMailList.mails:type_name -> pb.MailInfo
	0,  // 2: pb.S2CMailNotify.mail:type_name -> pb.MailInfo
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file__04_gamemail_proto_init() }
func file__04_gamemail_proto_init() {
	if File__04_gamemail_proto != nil {
		return
	}
	file_code_proto_init()
	file_gameconfig_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file__04_gamemail_proto_rawDesc), len(file__04_gamemail_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file__04_gamemail_proto_goTypes,
		DependencyIndexes: file__04_gamemail_proto_depIdxs,
		MessageInfos:      file__04_gamemail_proto_msgTypes,
	}.Build()
	File__04_gamemail_proto = out.File
	file__04_gamemail_proto_goTypes = nil
	file__04_gamemail_proto_depIdxs = nil
}
