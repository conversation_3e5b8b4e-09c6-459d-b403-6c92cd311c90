// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: 05.gameannouncement.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EAnnouncementType int32

const (
	EAnnouncementType_EAnnouncementType_None       EAnnouncementType = 0
	EAnnouncementType_EAnnouncementType_Marquee    EAnnouncementType = 1 // 跑马灯
	EAnnouncementType_EAnnouncementType_LoginPopup EAnnouncementType = 2 // 登录弹框
	EAnnouncementType_EAnnouncementType_Board      EAnnouncementType = 3 // 公告板
	EAnnouncementType_EAnnouncementType_Activity   EAnnouncementType = 4 // 活动公告
	EAnnouncementType_EAnnouncementType_System     EAnnouncementType = 5 // 系统公告
)

// Enum value maps for EAnnouncementType.
var (
	EAnnouncementType_name = map[int32]string{
		0: "EAnnouncementType_None",
		1: "EAnnouncementType_Marquee",
		2: "EAnnouncementType_LoginPopup",
		3: "EAnnouncementType_Board",
		4: "EAnnouncementType_Activity",
		5: "EAnnouncementType_System",
	}
	EAnnouncementType_value = map[string]int32{
		"EAnnouncementType_None":       0,
		"EAnnouncementType_Marquee":    1,
		"EAnnouncementType_LoginPopup": 2,
		"EAnnouncementType_Board":      3,
		"EAnnouncementType_Activity":   4,
		"EAnnouncementType_System":     5,
	}
)

func (x EAnnouncementType) Enum() *EAnnouncementType {
	p := new(EAnnouncementType)
	*p = x
	return p
}

func (x EAnnouncementType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAnnouncementType) Descriptor() protoreflect.EnumDescriptor {
	return file__05_gameannouncement_proto_enumTypes[0].Descriptor()
}

func (EAnnouncementType) Type() protoreflect.EnumType {
	return &file__05_gameannouncement_proto_enumTypes[0]
}

func (x EAnnouncementType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EAnnouncementType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EAnnouncementType(num)
	return nil
}

// Deprecated: Use EAnnouncementType.Descriptor instead.
func (EAnnouncementType) EnumDescriptor() ([]byte, []int) {
	return file__05_gameannouncement_proto_rawDescGZIP(), []int{0}
}

type EAnnouncementStatus int32

const (
	EAnnouncementStatus_EAnnouncementStatus_Unread EAnnouncementStatus = 1 // 未读
	EAnnouncementStatus_EAnnouncementStatus_Read   EAnnouncementStatus = 2 // 已读
)

// Enum value maps for EAnnouncementStatus.
var (
	EAnnouncementStatus_name = map[int32]string{
		1: "EAnnouncementStatus_Unread",
		2: "EAnnouncementStatus_Read",
	}
	EAnnouncementStatus_value = map[string]int32{
		"EAnnouncementStatus_Unread": 1,
		"EAnnouncementStatus_Read":   2,
	}
)

func (x EAnnouncementStatus) Enum() *EAnnouncementStatus {
	p := new(EAnnouncementStatus)
	*p = x
	return p
}

func (x EAnnouncementStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAnnouncementStatus) Descriptor() protoreflect.EnumDescriptor {
	return file__05_gameannouncement_proto_enumTypes[1].Descriptor()
}

func (EAnnouncementStatus) Type() protoreflect.EnumType {
	return &file__05_gameannouncement_proto_enumTypes[1]
}

func (x EAnnouncementStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EAnnouncementStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EAnnouncementStatus(num)
	return nil
}

// Deprecated: Use EAnnouncementStatus.Descriptor instead.
func (EAnnouncementStatus) EnumDescriptor() ([]byte, []int) {
	return file__05_gameannouncement_proto_rawDescGZIP(), []int{1}
}

// 公告信息
type AnnouncementInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint32                `protobuf:"varint,1,req,name=id" json:"id,omitempty"`                               // 公告ID
	Type          *EAnnouncementType     `protobuf:"varint,2,req,name=type,enum=pb.EAnnouncementType" json:"type,omitempty"` // 公告类型
	Title         *string                `protobuf:"bytes,3,req,name=title" json:"title,omitempty"`                          // 公告标题
	Content       *string                `protobuf:"bytes,4,req,name=content" json:"content,omitempty"`                      // 公告内容
	Status        *int32                 `protobuf:"varint,5,req,name=status" json:"status,omitempty"`                       // 公告状态: 1=未读, 2=已读
	Priority      *int32                 `protobuf:"varint,6,req,name=priority" json:"priority,omitempty"`                   // 优先级
	CreatedAt     *int64                 `protobuf:"varint,7,req,name=createdAt" json:"createdAt,omitempty"`                 // 创建时间
	StartTime     *int64                 `protobuf:"varint,8,req,name=startTime" json:"startTime,omitempty"`                 // 开始时间
	EndTime       *int64                 `protobuf:"varint,9,req,name=endTime" json:"endTime,omitempty"`                     // 结束时间
	ShowInterval  *int32                 `protobuf:"varint,10,opt,name=showInterval" json:"showInterval,omitempty"`          // 显示间隔(秒)
	JumpTarget    *string                `protobuf:"bytes,11,opt,name=jumpTarget" json:"jumpTarget,omitempty"`               // 跳转目标
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnnouncementInfo) Reset() {
	*x = AnnouncementInfo{}
	mi := &file__05_gameannouncement_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnouncementInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnouncementInfo) ProtoMessage() {}

func (x *AnnouncementInfo) ProtoReflect() protoreflect.Message {
	mi := &file__05_gameannouncement_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnouncementInfo.ProtoReflect.Descriptor instead.
func (*AnnouncementInfo) Descriptor() ([]byte, []int) {
	return file__05_gameannouncement_proto_rawDescGZIP(), []int{0}
}

func (x *AnnouncementInfo) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AnnouncementInfo) GetType() EAnnouncementType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EAnnouncementType_EAnnouncementType_None
}

func (x *AnnouncementInfo) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *AnnouncementInfo) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

func (x *AnnouncementInfo) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *AnnouncementInfo) GetPriority() int32 {
	if x != nil && x.Priority != nil {
		return *x.Priority
	}
	return 0
}

func (x *AnnouncementInfo) GetCreatedAt() int64 {
	if x != nil && x.CreatedAt != nil {
		return *x.CreatedAt
	}
	return 0
}

func (x *AnnouncementInfo) GetStartTime() int64 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *AnnouncementInfo) GetEndTime() int64 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *AnnouncementInfo) GetShowInterval() int32 {
	if x != nil && x.ShowInterval != nil {
		return *x.ShowInterval
	}
	return 0
}

func (x *AnnouncementInfo) GetJumpTarget() string {
	if x != nil && x.JumpTarget != nil {
		return *x.JumpTarget
	}
	return ""
}

// 公告列表返回
// 10200
// USED
type S2CAnnouncementList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Announcements []*AnnouncementInfo    `protobuf:"bytes,1,rep,name=announcements" json:"announcements,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CAnnouncementList) Reset() {
	*x = S2CAnnouncementList{}
	mi := &file__05_gameannouncement_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CAnnouncementList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAnnouncementList) ProtoMessage() {}

func (x *S2CAnnouncementList) ProtoReflect() protoreflect.Message {
	mi := &file__05_gameannouncement_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAnnouncementList.ProtoReflect.Descriptor instead.
func (*S2CAnnouncementList) Descriptor() ([]byte, []int) {
	return file__05_gameannouncement_proto_rawDescGZIP(), []int{1}
}

func (x *S2CAnnouncementList) GetAnnouncements() []*AnnouncementInfo {
	if x != nil {
		return x.Announcements
	}
	return nil
}

// 读取公告
// 10201
// USED
type C2SAnnouncementRead struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AnnouncementId *uint32                `protobuf:"varint,1,req,name=announcementId" json:"announcementId,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *C2SAnnouncementRead) Reset() {
	*x = C2SAnnouncementRead{}
	mi := &file__05_gameannouncement_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SAnnouncementRead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SAnnouncementRead) ProtoMessage() {}

func (x *C2SAnnouncementRead) ProtoReflect() protoreflect.Message {
	mi := &file__05_gameannouncement_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SAnnouncementRead.ProtoReflect.Descriptor instead.
func (*C2SAnnouncementRead) Descriptor() ([]byte, []int) {
	return file__05_gameannouncement_proto_rawDescGZIP(), []int{2}
}

func (x *C2SAnnouncementRead) GetAnnouncementId() uint32 {
	if x != nil && x.AnnouncementId != nil {
		return *x.AnnouncementId
	}
	return 0
}

// 读取公告返回
// 10202
// USED
type S2CAnnouncementRead struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	Announcement  *AnnouncementInfo      `protobuf:"bytes,3,req,name=announcement" json:"announcement,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CAnnouncementRead) Reset() {
	*x = S2CAnnouncementRead{}
	mi := &file__05_gameannouncement_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CAnnouncementRead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAnnouncementRead) ProtoMessage() {}

func (x *S2CAnnouncementRead) ProtoReflect() protoreflect.Message {
	mi := &file__05_gameannouncement_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAnnouncementRead.ProtoReflect.Descriptor instead.
func (*S2CAnnouncementRead) Descriptor() ([]byte, []int) {
	return file__05_gameannouncement_proto_rawDescGZIP(), []int{3}
}

func (x *S2CAnnouncementRead) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CAnnouncementRead) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CAnnouncementRead) GetAnnouncement() *AnnouncementInfo {
	if x != nil {
		return x.Announcement
	}
	return nil
}

// 新公告通知
// 10203
// USED
type S2CAnnouncementNotify struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Announcement  *AnnouncementInfo      `protobuf:"bytes,1,req,name=announcement" json:"announcement,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CAnnouncementNotify) Reset() {
	*x = S2CAnnouncementNotify{}
	mi := &file__05_gameannouncement_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CAnnouncementNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAnnouncementNotify) ProtoMessage() {}

func (x *S2CAnnouncementNotify) ProtoReflect() protoreflect.Message {
	mi := &file__05_gameannouncement_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAnnouncementNotify.ProtoReflect.Descriptor instead.
func (*S2CAnnouncementNotify) Descriptor() ([]byte, []int) {
	return file__05_gameannouncement_proto_rawDescGZIP(), []int{4}
}

func (x *S2CAnnouncementNotify) GetAnnouncement() *AnnouncementInfo {
	if x != nil {
		return x.Announcement
	}
	return nil
}

var File__05_gameannouncement_proto protoreflect.FileDescriptor

const file__05_gameannouncement_proto_rawDesc = "" +
	"\n" +
	"\x1905.gameannouncement.proto\x12\x02pb\x1a\n" +
	"code.proto\x1a\x10gameconfig.proto\"\xcb\x02\n" +
	"\x10AnnouncementInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x02(\rR\x02id\x12)\n" +
	"\x04type\x18\x02 \x02(\x0e2\x15.pb.EAnnouncementTypeR\x04type\x12\x14\n" +
	"\x05title\x18\x03 \x02(\tR\x05title\x12\x18\n" +
	"\acontent\x18\x04 \x02(\tR\acontent\x12\x16\n" +
	"\x06status\x18\x05 \x02(\x05R\x06status\x12\x1a\n" +
	"\bpriority\x18\x06 \x02(\x05R\bpriority\x12\x1c\n" +
	"\tcreatedAt\x18\a \x02(\x03R\tcreatedAt\x12\x1c\n" +
	"\tstartTime\x18\b \x02(\x03R\tstartTime\x12\x18\n" +
	"\aendTime\x18\t \x02(\x03R\aendTime\x12\"\n" +
	"\fshowInterval\x18\n" +
	" \x01(\x05R\fshowInterval\x12\x1e\n" +
	"\n" +
	"jumpTarget\x18\v \x01(\tR\n" +
	"jumpTarget\"Q\n" +
	"\x13S2CAnnouncementList\x12:\n" +
	"\rannouncements\x18\x01 \x03(\v2\x14.pb.AnnouncementInfoR\rannouncements\"=\n" +
	"\x13C2SAnnouncementRead\x12&\n" +
	"\x0eannouncementId\x18\x01 \x02(\rR\x0eannouncementId\"\x90\x01\n" +
	"\x13S2CAnnouncementRead\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x128\n" +
	"\fannouncement\x18\x03 \x02(\v2\x14.pb.AnnouncementInfoR\fannouncement\"Q\n" +
	"\x15S2CAnnouncementNotify\x128\n" +
	"\fannouncement\x18\x01 \x02(\v2\x14.pb.AnnouncementInfoR\fannouncement*\xcb\x01\n" +
	"\x11EAnnouncementType\x12\x1a\n" +
	"\x16EAnnouncementType_None\x10\x00\x12\x1d\n" +
	"\x19EAnnouncementType_Marquee\x10\x01\x12 \n" +
	"\x1cEAnnouncementType_LoginPopup\x10\x02\x12\x1b\n" +
	"\x17EAnnouncementType_Board\x10\x03\x12\x1e\n" +
	"\x1aEAnnouncementType_Activity\x10\x04\x12\x1c\n" +
	"\x18EAnnouncementType_System\x10\x05*S\n" +
	"\x13EAnnouncementStatus\x12\x1e\n" +
	"\x1aEAnnouncementStatus_Unread\x10\x01\x12\x1c\n" +
	"\x18EAnnouncementStatus_Read\x10\x02B&Z$kairo_paradise_server/services/pb;pb"

var (
	file__05_gameannouncement_proto_rawDescOnce sync.Once
	file__05_gameannouncement_proto_rawDescData []byte
)

func file__05_gameannouncement_proto_rawDescGZIP() []byte {
	file__05_gameannouncement_proto_rawDescOnce.Do(func() {
		file__05_gameannouncement_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file__05_gameannouncement_proto_rawDesc), len(file__05_gameannouncement_proto_rawDesc)))
	})
	return file__05_gameannouncement_proto_rawDescData
}

var file__05_gameannouncement_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file__05_gameannouncement_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file__05_gameannouncement_proto_goTypes = []any{
	(EAnnouncementType)(0),        // 0: pb.EAnnouncementType
	(EAnnouncementStatus)(0),      // 1: pb.EAnnouncementStatus
	(*AnnouncementInfo)(nil),      // 2: pb.AnnouncementInfo
	(*S2CAnnouncementList)(nil),   // 3: pb.S2CAnnouncementList
	(*C2SAnnouncementRead)(nil),   // 4: pb.C2SAnnouncementRead
	(*S2CAnnouncementRead)(nil),   // 5: pb.S2CAnnouncementRead
	(*S2CAnnouncementNotify)(nil), // 6: pb.S2CAnnouncementNotify
	(ResponseCode)(0),             // 7: pb.response_code
}
var file__05_gameannouncement_proto_depIdxs = []int32{
	0, // 0: pb.AnnouncementInfo.type:type_name -> pb.EAnnouncementType
	2, // 1: pb.S2CAnnouncementList.announcements:type_name -> pb.AnnouncementInfo
	7, // 2: pb.S2CAnnouncementRead.code:type_name -> pb.response_code
	2, // 3: pb.S2CAnnouncementRead.announcement:type_name -> pb.AnnouncementInfo
	2, // 4: pb.S2CAnnouncementNotify.announcement:type_name -> pb.AnnouncementInfo
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file__05_gameannouncement_proto_init() }
func file__05_gameannouncement_proto_init() {
	if File__05_gameannouncement_proto != nil {
		return
	}
	file_code_proto_init()
	file_gameconfig_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file__05_gameannouncement_proto_rawDesc), len(file__05_gameannouncement_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file__05_gameannouncement_proto_goTypes,
		DependencyIndexes: file__05_gameannouncement_proto_depIdxs,
		EnumInfos:         file__05_gameannouncement_proto_enumTypes,
		MessageInfos:      file__05_gameannouncement_proto_msgTypes,
	}.Build()
	File__05_gameannouncement_proto = out.File
	file__05_gameannouncement_proto_goTypes = nil
	file__05_gameannouncement_proto_depIdxs = nil
}
