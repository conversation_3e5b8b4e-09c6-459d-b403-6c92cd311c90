// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: 08.gamegm.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 客户端发送GM
// 10350
// USED
type C2SGM struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cmd      param（itemId;数量）      desc
	// addItem  4;100                    添加物品
	// subItem  1;100                    扣减物品
	// ClearItem                         清空背包
	Cmd           *string `protobuf:"bytes,1,req,name=cmd" json:"cmd,omitempty"`
	Params        *string `protobuf:"bytes,2,req,name=params" json:"params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SGM) Reset() {
	*x = C2SGM{}
	mi := &file__08_gamegm_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SGM) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SGM) ProtoMessage() {}

func (x *C2SGM) ProtoReflect() protoreflect.Message {
	mi := &file__08_gamegm_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SGM.ProtoReflect.Descriptor instead.
func (*C2SGM) Descriptor() ([]byte, []int) {
	return file__08_gamegm_proto_rawDescGZIP(), []int{0}
}

func (x *C2SGM) GetCmd() string {
	if x != nil && x.Cmd != nil {
		return *x.Cmd
	}
	return ""
}

func (x *C2SGM) GetParams() string {
	if x != nil && x.Params != nil {
		return *x.Params
	}
	return ""
}

// GM发送邮件
// 10351
// USED
type C2SGMSendMail struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// required uint32 playerId = 1;  // 玩家ID，0表示全服邮件
	Title         *string     `protobuf:"bytes,2,req,name=title" json:"title,omitempty"`                  // 邮件标题
	Content       *string     `protobuf:"bytes,3,req,name=content" json:"content,omitempty"`              // 邮件内容
	Sender        *string     `protobuf:"bytes,4,req,name=sender" json:"sender,omitempty"`                // 发送者
	Attachments   []*ItemData `protobuf:"bytes,5,rep,name=attachments" json:"attachments,omitempty"`      // 附件
	ExpireSeconds *int64      `protobuf:"varint,6,opt,name=expireSeconds" json:"expireSeconds,omitempty"` // 过期时间（秒），0表示永不过期
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SGMSendMail) Reset() {
	*x = C2SGMSendMail{}
	mi := &file__08_gamegm_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SGMSendMail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SGMSendMail) ProtoMessage() {}

func (x *C2SGMSendMail) ProtoReflect() protoreflect.Message {
	mi := &file__08_gamegm_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SGMSendMail.ProtoReflect.Descriptor instead.
func (*C2SGMSendMail) Descriptor() ([]byte, []int) {
	return file__08_gamegm_proto_rawDescGZIP(), []int{1}
}

func (x *C2SGMSendMail) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *C2SGMSendMail) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

func (x *C2SGMSendMail) GetSender() string {
	if x != nil && x.Sender != nil {
		return *x.Sender
	}
	return ""
}

func (x *C2SGMSendMail) GetAttachments() []*ItemData {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *C2SGMSendMail) GetExpireSeconds() int64 {
	if x != nil && x.ExpireSeconds != nil {
		return *x.ExpireSeconds
	}
	return 0
}

var File__08_gamegm_proto protoreflect.FileDescriptor

const file__08_gamegm_proto_rawDesc = "" +
	"\n" +
	"\x0f08.gamegm.proto\x12\x02pb\x1a\x10gameconfig.proto\x1a\x1002.gamebag.proto\"1\n" +
	"\x05C2SGM\x12\x10\n" +
	"\x03cmd\x18\x01 \x02(\tR\x03cmd\x12\x16\n" +
	"\x06params\x18\x02 \x02(\tR\x06params\"\xad\x01\n" +
	"\rC2SGMSendMail\x12\x14\n" +
	"\x05title\x18\x02 \x02(\tR\x05title\x12\x18\n" +
	"\acontent\x18\x03 \x02(\tR\acontent\x12\x16\n" +
	"\x06sender\x18\x04 \x02(\tR\x06sender\x12.\n" +
	"\vattachments\x18\x05 \x03(\v2\f.pb.ItemDataR\vattachments\x12$\n" +
	"\rexpireSeconds\x18\x06 \x01(\x03R\rexpireSecondsB&Z$kairo_paradise_server/services/pb;pb"

var (
	file__08_gamegm_proto_rawDescOnce sync.Once
	file__08_gamegm_proto_rawDescData []byte
)

func file__08_gamegm_proto_rawDescGZIP() []byte {
	file__08_gamegm_proto_rawDescOnce.Do(func() {
		file__08_gamegm_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file__08_gamegm_proto_rawDesc), len(file__08_gamegm_proto_rawDesc)))
	})
	return file__08_gamegm_proto_rawDescData
}

var file__08_gamegm_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file__08_gamegm_proto_goTypes = []any{
	(*C2SGM)(nil),         // 0: pb.C2SGM
	(*C2SGMSendMail)(nil), // 1: pb.C2SGMSendMail
	(*ItemData)(nil),      // 2: pb.ItemData
}
var file__08_gamegm_proto_depIdxs = []int32{
	2, // 0: pb.C2SGMSendMail.attachments:type_name -> pb.ItemData
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file__08_gamegm_proto_init() }
func file__08_gamegm_proto_init() {
	if File__08_gamegm_proto != nil {
		return
	}
	file_gameconfig_proto_init()
	file__02_gamebag_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file__08_gamegm_proto_rawDesc), len(file__08_gamegm_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file__08_gamegm_proto_goTypes,
		DependencyIndexes: file__08_gamegm_proto_depIdxs,
		MessageInfos:      file__08_gamegm_proto_msgTypes,
	}.Build()
	File__08_gamegm_proto = out.File
	file__08_gamegm_proto_goTypes = nil
	file__08_gamegm_proto_depIdxs = nil
}
