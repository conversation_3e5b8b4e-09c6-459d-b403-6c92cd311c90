// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: pubicrank.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 排行榜数据
type RankEntryData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerId      *uint64                `protobuf:"varint,1,req,name=player_id,json=playerId" json:"player_id,omitempty"`      // 玩家ID
	PlayerName    *string                `protobuf:"bytes,2,opt,name=player_name,json=playerName" json:"player_name,omitempty"` // 玩家名称
	Level         *int32                 `protobuf:"varint,3,opt,name=level" json:"level,omitempty"`                            // 玩家等级
	Prosperity    *int32                 `protobuf:"varint,4,opt,name=prosperity" json:"prosperity,omitempty"`                  // 繁荣度
	Score         *int32                 `protobuf:"varint,5,opt,name=score" json:"score,omitempty"`                            // 分数
	Ranking       *int32                 `protobuf:"varint,6,opt,name=ranking" json:"ranking,omitempty"`                        // 排行
	Icon          *int32                 `protobuf:"varint,7,opt,name=icon" json:"icon,omitempty"`                              // 玩家头像
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RankEntryData) Reset() {
	*x = RankEntryData{}
	mi := &file_pubicrank_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RankEntryData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankEntryData) ProtoMessage() {}

func (x *RankEntryData) ProtoReflect() protoreflect.Message {
	mi := &file_pubicrank_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankEntryData.ProtoReflect.Descriptor instead.
func (*RankEntryData) Descriptor() ([]byte, []int) {
	return file_pubicrank_proto_rawDescGZIP(), []int{0}
}

func (x *RankEntryData) GetPlayerId() uint64 {
	if x != nil && x.PlayerId != nil {
		return *x.PlayerId
	}
	return 0
}

func (x *RankEntryData) GetPlayerName() string {
	if x != nil && x.PlayerName != nil {
		return *x.PlayerName
	}
	return ""
}

func (x *RankEntryData) GetLevel() int32 {
	if x != nil && x.Level != nil {
		return *x.Level
	}
	return 0
}

func (x *RankEntryData) GetProsperity() int32 {
	if x != nil && x.Prosperity != nil {
		return *x.Prosperity
	}
	return 0
}

func (x *RankEntryData) GetScore() int32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *RankEntryData) GetRanking() int32 {
	if x != nil && x.Ranking != nil {
		return *x.Ranking
	}
	return 0
}

func (x *RankEntryData) GetIcon() int32 {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return 0
}

// 更新排行榜数据请求
// 30100
// USED
type G2SUpdateRank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RankType      *ERankType             `protobuf:"varint,1,req,name=rank_type,json=rankType,enum=pb.ERankType" json:"rank_type,omitempty"` // 排行榜类型
	Entry         *RankEntryData         `protobuf:"bytes,2,req,name=entry" json:"entry,omitempty"`                                          // 排行榜条目数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *G2SUpdateRank) Reset() {
	*x = G2SUpdateRank{}
	mi := &file_pubicrank_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *G2SUpdateRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2SUpdateRank) ProtoMessage() {}

func (x *G2SUpdateRank) ProtoReflect() protoreflect.Message {
	mi := &file_pubicrank_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2SUpdateRank.ProtoReflect.Descriptor instead.
func (*G2SUpdateRank) Descriptor() ([]byte, []int) {
	return file_pubicrank_proto_rawDescGZIP(), []int{1}
}

func (x *G2SUpdateRank) GetRankType() ERankType {
	if x != nil && x.RankType != nil {
		return *x.RankType
	}
	return ERankType_ERankType_None
}

func (x *G2SUpdateRank) GetEntry() *RankEntryData {
	if x != nil {
		return x.Entry
	}
	return nil
}

// 更新排行榜数据响应
// 30101
// USED
type S2GUpdateRank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"` // 响应码
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`                 // 错误信息
	Rank          *int32                 `protobuf:"varint,3,opt,name=rank" json:"rank,omitempty"`                      // 更新后的排名，如果未上榜则为-1
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2GUpdateRank) Reset() {
	*x = S2GUpdateRank{}
	mi := &file_pubicrank_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2GUpdateRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2GUpdateRank) ProtoMessage() {}

func (x *S2GUpdateRank) ProtoReflect() protoreflect.Message {
	mi := &file_pubicrank_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2GUpdateRank.ProtoReflect.Descriptor instead.
func (*S2GUpdateRank) Descriptor() ([]byte, []int) {
	return file_pubicrank_proto_rawDescGZIP(), []int{2}
}

func (x *S2GUpdateRank) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2GUpdateRank) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2GUpdateRank) GetRank() int32 {
	if x != nil && x.Rank != nil {
		return *x.Rank
	}
	return 0
}

// 获取排行榜数据请求
// 30102
// USED
type C2SGetRankList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RankType      *ERankType             `protobuf:"varint,1,req,name=rank_type,json=rankType,enum=pb.ERankType" json:"rank_type,omitempty"` // 排行榜类型
	Start         *int32                 `protobuf:"varint,2,req,name=start" json:"start,omitempty"`                                         // 起始排名（从1开始）
	Count         *int32                 `protobuf:"varint,3,req,name=count" json:"count,omitempty"`                                         // 获取数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SGetRankList) Reset() {
	*x = C2SGetRankList{}
	mi := &file_pubicrank_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SGetRankList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SGetRankList) ProtoMessage() {}

func (x *C2SGetRankList) ProtoReflect() protoreflect.Message {
	mi := &file_pubicrank_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SGetRankList.ProtoReflect.Descriptor instead.
func (*C2SGetRankList) Descriptor() ([]byte, []int) {
	return file_pubicrank_proto_rawDescGZIP(), []int{3}
}

func (x *C2SGetRankList) GetRankType() ERankType {
	if x != nil && x.RankType != nil {
		return *x.RankType
	}
	return ERankType_ERankType_None
}

func (x *C2SGetRankList) GetStart() int32 {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return 0
}

func (x *C2SGetRankList) GetCount() int32 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

// 获取排行榜数据响应
// 30103
// USED
type S2CGetRankList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"`          // 响应码
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`                          // 错误信息
	Entries       []*RankEntryData       `protobuf:"bytes,3,rep,name=entries" json:"entries,omitempty"`                          // 排行榜数据
	TotalCount    *int32                 `protobuf:"varint,4,opt,name=total_count,json=totalCount" json:"total_count,omitempty"` // 排行榜总条目数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CGetRankList) Reset() {
	*x = S2CGetRankList{}
	mi := &file_pubicrank_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CGetRankList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CGetRankList) ProtoMessage() {}

func (x *S2CGetRankList) ProtoReflect() protoreflect.Message {
	mi := &file_pubicrank_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CGetRankList.ProtoReflect.Descriptor instead.
func (*S2CGetRankList) Descriptor() ([]byte, []int) {
	return file_pubicrank_proto_rawDescGZIP(), []int{4}
}

func (x *S2CGetRankList) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CGetRankList) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CGetRankList) GetEntries() []*RankEntryData {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *S2CGetRankList) GetTotalCount() int32 {
	if x != nil && x.TotalCount != nil {
		return *x.TotalCount
	}
	return 0
}

// 获取玩家排名请求
// 30104
// USED
type G2SGetPlayerRank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RankType      *ERankType             `protobuf:"varint,1,req,name=rank_type,json=rankType,enum=pb.ERankType" json:"rank_type,omitempty"` // 排行榜类型
	PlayerId      *uint64                `protobuf:"varint,2,req,name=player_id,json=playerId" json:"player_id,omitempty"`                   // 玩家ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *G2SGetPlayerRank) Reset() {
	*x = G2SGetPlayerRank{}
	mi := &file_pubicrank_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *G2SGetPlayerRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2SGetPlayerRank) ProtoMessage() {}

func (x *G2SGetPlayerRank) ProtoReflect() protoreflect.Message {
	mi := &file_pubicrank_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2SGetPlayerRank.ProtoReflect.Descriptor instead.
func (*G2SGetPlayerRank) Descriptor() ([]byte, []int) {
	return file_pubicrank_proto_rawDescGZIP(), []int{5}
}

func (x *G2SGetPlayerRank) GetRankType() ERankType {
	if x != nil && x.RankType != nil {
		return *x.RankType
	}
	return ERankType_ERankType_None
}

func (x *G2SGetPlayerRank) GetPlayerId() uint64 {
	if x != nil && x.PlayerId != nil {
		return *x.PlayerId
	}
	return 0
}

// 获取玩家排名响应
// 30105
// USED
type S2GGetPlayerRank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"` // 响应码
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`                 // 错误信息
	Rank          *int32                 `protobuf:"varint,3,opt,name=rank" json:"rank,omitempty"`                      // 玩家排名，如果未上榜则为-1
	Entry         *RankEntryData         `protobuf:"bytes,4,opt,name=entry" json:"entry,omitempty"`                     // 玩家排行榜数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2GGetPlayerRank) Reset() {
	*x = S2GGetPlayerRank{}
	mi := &file_pubicrank_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2GGetPlayerRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2GGetPlayerRank) ProtoMessage() {}

func (x *S2GGetPlayerRank) ProtoReflect() protoreflect.Message {
	mi := &file_pubicrank_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2GGetPlayerRank.ProtoReflect.Descriptor instead.
func (*S2GGetPlayerRank) Descriptor() ([]byte, []int) {
	return file_pubicrank_proto_rawDescGZIP(), []int{6}
}

func (x *S2GGetPlayerRank) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2GGetPlayerRank) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2GGetPlayerRank) GetRank() int32 {
	if x != nil && x.Rank != nil {
		return *x.Rank
	}
	return 0
}

func (x *S2GGetPlayerRank) GetEntry() *RankEntryData {
	if x != nil {
		return x.Entry
	}
	return nil
}

// 清除排行榜请求（仅管理员使用）
// 30106
// USED
type G2SClearRank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RankType      *ERankType             `protobuf:"varint,1,req,name=rank_type,json=rankType,enum=pb.ERankType" json:"rank_type,omitempty"` // 排行榜类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *G2SClearRank) Reset() {
	*x = G2SClearRank{}
	mi := &file_pubicrank_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *G2SClearRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2SClearRank) ProtoMessage() {}

func (x *G2SClearRank) ProtoReflect() protoreflect.Message {
	mi := &file_pubicrank_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2SClearRank.ProtoReflect.Descriptor instead.
func (*G2SClearRank) Descriptor() ([]byte, []int) {
	return file_pubicrank_proto_rawDescGZIP(), []int{7}
}

func (x *G2SClearRank) GetRankType() ERankType {
	if x != nil && x.RankType != nil {
		return *x.RankType
	}
	return ERankType_ERankType_None
}

var File_pubicrank_proto protoreflect.FileDescriptor

const file_pubicrank_proto_rawDesc = "" +
	"\n" +
	"\x0fpubicrank.proto\x12\x02pb\x1a\n" +
	"code.proto\x1a\x10gameconfig.proto\"\xc7\x01\n" +
	"\rRankEntryData\x12\x1b\n" +
	"\tplayer_id\x18\x01 \x02(\x04R\bplayerId\x12\x1f\n" +
	"\vplayer_name\x18\x02 \x01(\tR\n" +
	"playerName\x12\x14\n" +
	"\x05level\x18\x03 \x01(\x05R\x05level\x12\x1e\n" +
	"\n" +
	"prosperity\x18\x04 \x01(\x05R\n" +
	"prosperity\x12\x14\n" +
	"\x05score\x18\x05 \x01(\x05R\x05score\x12\x18\n" +
	"\aranking\x18\x06 \x01(\x05R\aranking\x12\x12\n" +
	"\x04icon\x18\a \x01(\x05R\x04icon\"d\n" +
	"\rG2SUpdateRank\x12*\n" +
	"\trank_type\x18\x01 \x02(\x0e2\r.pb.ERankTypeR\brankType\x12'\n" +
	"\x05entry\x18\x02 \x02(\v2\x11.pb.RankEntryDataR\x05entry\"d\n" +
	"\rS2GUpdateRank\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04rank\x18\x03 \x01(\x05R\x04rank\"h\n" +
	"\x0eC2SGetRankList\x12*\n" +
	"\trank_type\x18\x01 \x02(\x0e2\r.pb.ERankTypeR\brankType\x12\x14\n" +
	"\x05start\x18\x02 \x02(\x05R\x05start\x12\x14\n" +
	"\x05count\x18\x03 \x02(\x05R\x05count\"\x9f\x01\n" +
	"\x0eS2CGetRankList\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12+\n" +
	"\aentries\x18\x03 \x03(\v2\x11.pb.RankEntryDataR\aentries\x12\x1f\n" +
	"\vtotal_count\x18\x04 \x01(\x05R\n" +
	"totalCount\"[\n" +
	"\x10G2SGetPlayerRank\x12*\n" +
	"\trank_type\x18\x01 \x02(\x0e2\r.pb.ERankTypeR\brankType\x12\x1b\n" +
	"\tplayer_id\x18\x02 \x02(\x04R\bplayerId\"\x90\x01\n" +
	"\x10S2GGetPlayerRank\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04rank\x18\x03 \x01(\x05R\x04rank\x12'\n" +
	"\x05entry\x18\x04 \x01(\v2\x11.pb.RankEntryDataR\x05entry\":\n" +
	"\fG2SClearRank\x12*\n" +
	"\trank_type\x18\x01 \x02(\x0e2\r.pb.ERankTypeR\brankTypeB&Z$kairo_paradise_server/services/pb;pb"

var (
	file_pubicrank_proto_rawDescOnce sync.Once
	file_pubicrank_proto_rawDescData []byte
)

func file_pubicrank_proto_rawDescGZIP() []byte {
	file_pubicrank_proto_rawDescOnce.Do(func() {
		file_pubicrank_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pubicrank_proto_rawDesc), len(file_pubicrank_proto_rawDesc)))
	})
	return file_pubicrank_proto_rawDescData
}

var file_pubicrank_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pubicrank_proto_goTypes = []any{
	(*RankEntryData)(nil),    // 0: pb.RankEntryData
	(*G2SUpdateRank)(nil),    // 1: pb.G2SUpdateRank
	(*S2GUpdateRank)(nil),    // 2: pb.S2GUpdateRank
	(*C2SGetRankList)(nil),   // 3: pb.C2SGetRankList
	(*S2CGetRankList)(nil),   // 4: pb.S2CGetRankList
	(*G2SGetPlayerRank)(nil), // 5: pb.G2SGetPlayerRank
	(*S2GGetPlayerRank)(nil), // 6: pb.S2GGetPlayerRank
	(*G2SClearRank)(nil),     // 7: pb.G2SClearRank
	(ERankType)(0),           // 8: pb.ERankType
	(ResponseCode)(0),        // 9: pb.response_code
}
var file_pubicrank_proto_depIdxs = []int32{
	8,  // 0: pb.G2SUpdateRank.rank_type:type_name -> pb.ERankType
	0,  // 1: pb.G2SUpdateRank.entry:type_name -> pb.RankEntryData
	9,  // 2: pb.S2GUpdateRank.code:type_name -> pb.response_code
	8,  // 3: pb.C2SGetRankList.rank_type:type_name -> pb.ERankType
	9,  // 4: pb.S2CGetRankList.code:type_name -> pb.response_code
	0,  // 5: pb.S2CGetRankList.entries:type_name -> pb.RankEntryData
	8,  // 6: pb.G2SGetPlayerRank.rank_type:type_name -> pb.ERankType
	9,  // 7: pb.S2GGetPlayerRank.code:type_name -> pb.response_code
	0,  // 8: pb.S2GGetPlayerRank.entry:type_name -> pb.RankEntryData
	8,  // 9: pb.G2SClearRank.rank_type:type_name -> pb.ERankType
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_pubicrank_proto_init() }
func file_pubicrank_proto_init() {
	if File_pubicrank_proto != nil {
		return
	}
	file_code_proto_init()
	file_gameconfig_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pubicrank_proto_rawDesc), len(file_pubicrank_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pubicrank_proto_goTypes,
		DependencyIndexes: file_pubicrank_proto_depIdxs,
		MessageInfos:      file_pubicrank_proto_msgTypes,
	}.Build()
	File_pubicrank_proto = out.File
	file_pubicrank_proto_goTypes = nil
	file_pubicrank_proto_depIdxs = nil
}
