// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: 01.game.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeviceType int32

const (
	DeviceType_unknown DeviceType = 0
	DeviceType_editor  DeviceType = 1
	DeviceType_android DeviceType = 2
	DeviceType_iOS     DeviceType = 4
	DeviceType_pc      DeviceType = 5
	DeviceType_macOS   DeviceType = 6
	DeviceType_linux   DeviceType = 7
	DeviceType_webGL   DeviceType = 8
	DeviceType_PS      DeviceType = 9
	DeviceType_XboxOne DeviceType = 10
)

// Enum value maps for DeviceType.
var (
	DeviceType_name = map[int32]string{
		0:  "unknown",
		1:  "editor",
		2:  "android",
		4:  "iOS",
		5:  "pc",
		6:  "macOS",
		7:  "linux",
		8:  "webGL",
		9:  "PS",
		10: "XboxOne",
	}
	DeviceType_value = map[string]int32{
		"unknown": 0,
		"editor":  1,
		"android": 2,
		"iOS":     4,
		"pc":      5,
		"macOS":   6,
		"linux":   7,
		"webGL":   8,
		"PS":      9,
		"XboxOne": 10,
	}
)

func (x DeviceType) Enum() *DeviceType {
	p := new(DeviceType)
	*p = x
	return p
}

func (x DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file__01_game_proto_enumTypes[0].Descriptor()
}

func (DeviceType) Type() protoreflect.EnumType {
	return &file__01_game_proto_enumTypes[0]
}

func (x DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DeviceType(num)
	return nil
}

// Deprecated: Use DeviceType.Descriptor instead.
func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return file__01_game_proto_rawDescGZIP(), []int{0}
}

// 通用返回协议
// 10000
// USED
type S2CCommResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CCommResponse) Reset() {
	*x = S2CCommResponse{}
	mi := &file__01_game_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CCommResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CCommResponse) ProtoMessage() {}

func (x *S2CCommResponse) ProtoReflect() protoreflect.Message {
	mi := &file__01_game_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CCommResponse.ProtoReflect.Descriptor instead.
func (*S2CCommResponse) Descriptor() ([]byte, []int) {
	return file__01_game_proto_rawDescGZIP(), []int{0}
}

func (x *S2CCommResponse) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CCommResponse) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

// 进入游戏
// 10001
// USED
type C2SEnterGame struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	DeviceType        *DeviceType            `protobuf:"varint,1,opt,name=deviceType,enum=pb.DeviceType" json:"deviceType,omitempty"`
	ClientProtocolVer *string                `protobuf:"bytes,2,opt,name=clientProtocolVer" json:"clientProtocolVer,omitempty"`
	IsReconnect       *bool                  `protobuf:"varint,3,opt,name=isReconnect" json:"isReconnect,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *C2SEnterGame) Reset() {
	*x = C2SEnterGame{}
	mi := &file__01_game_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SEnterGame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SEnterGame) ProtoMessage() {}

func (x *C2SEnterGame) ProtoReflect() protoreflect.Message {
	mi := &file__01_game_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SEnterGame.ProtoReflect.Descriptor instead.
func (*C2SEnterGame) Descriptor() ([]byte, []int) {
	return file__01_game_proto_rawDescGZIP(), []int{1}
}

func (x *C2SEnterGame) GetDeviceType() DeviceType {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return DeviceType_unknown
}

func (x *C2SEnterGame) GetClientProtocolVer() string {
	if x != nil && x.ClientProtocolVer != nil {
		return *x.ClientProtocolVer
	}
	return ""
}

func (x *C2SEnterGame) GetIsReconnect() bool {
	if x != nil && x.IsReconnect != nil {
		return *x.IsReconnect
	}
	return false
}

// 进入游戏加载完成通知
// 10002
// USED
type S2CEnterGame struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	IsRegister    *bool                  `protobuf:"varint,2,req,name=isRegister" json:"isRegister,omitempty"` // 是否新创建角色
	Message       *string                `protobuf:"bytes,3,opt,name=message" json:"message,omitempty"`
	PlayerId      *uint64                `protobuf:"varint,4,req,name=playerId" json:"playerId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CEnterGame) Reset() {
	*x = S2CEnterGame{}
	mi := &file__01_game_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CEnterGame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CEnterGame) ProtoMessage() {}

func (x *S2CEnterGame) ProtoReflect() protoreflect.Message {
	mi := &file__01_game_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CEnterGame.ProtoReflect.Descriptor instead.
func (*S2CEnterGame) Descriptor() ([]byte, []int) {
	return file__01_game_proto_rawDescGZIP(), []int{2}
}

func (x *S2CEnterGame) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CEnterGame) GetIsRegister() bool {
	if x != nil && x.IsRegister != nil {
		return *x.IsRegister
	}
	return false
}

func (x *S2CEnterGame) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CEnterGame) GetPlayerId() uint64 {
	if x != nil && x.PlayerId != nil {
		return *x.PlayerId
	}
	return 0
}

// 获取玩家信息
// 10003
// USED
type S2CPlayerInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	Id            *uint64                `protobuf:"varint,2,req,name=id" json:"id,omitempty"`
	Nick          *string                `protobuf:"bytes,3,req,name=nick" json:"nick,omitempty"`
	Avatar        []int32                `protobuf:"varint,4,rep,name=avatar" json:"avatar,omitempty"`
	Gender        *EGender               `protobuf:"varint,5,req,name=gender,enum=pb.EGender" json:"gender,omitempty"`
	Message       *string                `protobuf:"bytes,6,opt,name=message" json:"message,omitempty"`
	Icon          *int32                 `protobuf:"varint,7,opt,name=icon" json:"icon,omitempty"`
	Level         *int32                 `protobuf:"varint,8,opt,name=level" json:"level,omitempty"`           // 世界等级
	Prosperity    *int32                 `protobuf:"varint,9,opt,name=prosperity" json:"prosperity,omitempty"` // 富裕度
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CPlayerInfo) Reset() {
	*x = S2CPlayerInfo{}
	mi := &file__01_game_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CPlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CPlayerInfo) ProtoMessage() {}

func (x *S2CPlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file__01_game_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CPlayerInfo.ProtoReflect.Descriptor instead.
func (*S2CPlayerInfo) Descriptor() ([]byte, []int) {
	return file__01_game_proto_rawDescGZIP(), []int{3}
}

func (x *S2CPlayerInfo) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CPlayerInfo) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *S2CPlayerInfo) GetNick() string {
	if x != nil && x.Nick != nil {
		return *x.Nick
	}
	return ""
}

func (x *S2CPlayerInfo) GetAvatar() []int32 {
	if x != nil {
		return x.Avatar
	}
	return nil
}

func (x *S2CPlayerInfo) GetGender() EGender {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return EGender_EGender_Invalid
}

func (x *S2CPlayerInfo) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CPlayerInfo) GetIcon() int32 {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return 0
}

func (x *S2CPlayerInfo) GetLevel() int32 {
	if x != nil && x.Level != nil {
		return *x.Level
	}
	return 0
}

func (x *S2CPlayerInfo) GetProsperity() int32 {
	if x != nil && x.Prosperity != nil {
		return *x.Prosperity
	}
	return 0
}

// 登录完成通知
// 10004
// USED
type S2CLoginFinish struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CLoginFinish) Reset() {
	*x = S2CLoginFinish{}
	mi := &file__01_game_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CLoginFinish) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CLoginFinish) ProtoMessage() {}

func (x *S2CLoginFinish) ProtoReflect() protoreflect.Message {
	mi := &file__01_game_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CLoginFinish.ProtoReflect.Descriptor instead.
func (*S2CLoginFinish) Descriptor() ([]byte, []int) {
	return file__01_game_proto_rawDescGZIP(), []int{4}
}

func (x *S2CLoginFinish) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CLoginFinish) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

// 修改基本信息
// 10005
// USED
type C2SPlayerModifyInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nick          *string                `protobuf:"bytes,1,req,name=nick" json:"nick,omitempty"`
	Avatar        []int32                `protobuf:"varint,2,rep,name=avatar" json:"avatar,omitempty"`
	Gender        *EGender               `protobuf:"varint,3,req,name=gender,enum=pb.EGender" json:"gender,omitempty"`
	Icon          *int32                 `protobuf:"varint,4,req,name=icon" json:"icon,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SPlayerModifyInfo) Reset() {
	*x = C2SPlayerModifyInfo{}
	mi := &file__01_game_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SPlayerModifyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SPlayerModifyInfo) ProtoMessage() {}

func (x *C2SPlayerModifyInfo) ProtoReflect() protoreflect.Message {
	mi := &file__01_game_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SPlayerModifyInfo.ProtoReflect.Descriptor instead.
func (*C2SPlayerModifyInfo) Descriptor() ([]byte, []int) {
	return file__01_game_proto_rawDescGZIP(), []int{5}
}

func (x *C2SPlayerModifyInfo) GetNick() string {
	if x != nil && x.Nick != nil {
		return *x.Nick
	}
	return ""
}

func (x *C2SPlayerModifyInfo) GetAvatar() []int32 {
	if x != nil {
		return x.Avatar
	}
	return nil
}

func (x *C2SPlayerModifyInfo) GetGender() EGender {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return EGender_EGender_Invalid
}

func (x *C2SPlayerModifyInfo) GetIcon() int32 {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return 0
}

// 等级变更协议
// 10006
// USED
type S2CPlayerModifyLevel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Level         *int32                 `protobuf:"varint,1,opt,name=level" json:"level,omitempty"`           // 世界等级
	Prosperity    *int32                 `protobuf:"varint,2,opt,name=prosperity" json:"prosperity,omitempty"` // 富裕度
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CPlayerModifyLevel) Reset() {
	*x = S2CPlayerModifyLevel{}
	mi := &file__01_game_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CPlayerModifyLevel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CPlayerModifyLevel) ProtoMessage() {}

func (x *S2CPlayerModifyLevel) ProtoReflect() protoreflect.Message {
	mi := &file__01_game_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CPlayerModifyLevel.ProtoReflect.Descriptor instead.
func (*S2CPlayerModifyLevel) Descriptor() ([]byte, []int) {
	return file__01_game_proto_rawDescGZIP(), []int{6}
}

func (x *S2CPlayerModifyLevel) GetLevel() int32 {
	if x != nil && x.Level != nil {
		return *x.Level
	}
	return 0
}

func (x *S2CPlayerModifyLevel) GetProsperity() int32 {
	if x != nil && x.Prosperity != nil {
		return *x.Prosperity
	}
	return 0
}

// 积分变更协议
// 10007
// USED
type S2CPlayerProsperityUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Prosperity    *int32                 `protobuf:"varint,2,opt,name=prosperity" json:"prosperity,omitempty"` // 富裕度
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CPlayerProsperityUpdate) Reset() {
	*x = S2CPlayerProsperityUpdate{}
	mi := &file__01_game_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CPlayerProsperityUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CPlayerProsperityUpdate) ProtoMessage() {}

func (x *S2CPlayerProsperityUpdate) ProtoReflect() protoreflect.Message {
	mi := &file__01_game_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CPlayerProsperityUpdate.ProtoReflect.Descriptor instead.
func (*S2CPlayerProsperityUpdate) Descriptor() ([]byte, []int) {
	return file__01_game_proto_rawDescGZIP(), []int{7}
}

func (x *S2CPlayerProsperityUpdate) GetProsperity() int32 {
	if x != nil && x.Prosperity != nil {
		return *x.Prosperity
	}
	return 0
}

var File__01_game_proto protoreflect.FileDescriptor

const file__01_game_proto_rawDesc = "" +
	"\n" +
	"\r01.game.proto\x12\x02pb\x1a\n" +
	"code.proto\x1a\x10gameconfig.proto\"R\n" +
	"\x0fS2CCommResponse\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x8f\x01\n" +
	"\fC2SEnterGame\x12/\n" +
	"\n" +
	"deviceType\x18\x01 \x01(\x0e2\x0f.pb.device_typeR\n" +
	"deviceType\x12,\n" +
	"\x11clientProtocolVer\x18\x02 \x01(\tR\x11clientProtocolVer\x12 \n" +
	"\visReconnect\x18\x03 \x01(\bR\visReconnect\"\x8b\x01\n" +
	"\fS2CEnterGame\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x1e\n" +
	"\n" +
	"isRegister\x18\x02 \x02(\bR\n" +
	"isRegister\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12\x1a\n" +
	"\bplayerId\x18\x04 \x02(\x04R\bplayerId\"\xfb\x01\n" +
	"\rS2CPlayerInfo\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x0e\n" +
	"\x02id\x18\x02 \x02(\x04R\x02id\x12\x12\n" +
	"\x04nick\x18\x03 \x02(\tR\x04nick\x12\x16\n" +
	"\x06avatar\x18\x04 \x03(\x05R\x06avatar\x12#\n" +
	"\x06gender\x18\x05 \x02(\x0e2\v.pb.EGenderR\x06gender\x12\x18\n" +
	"\amessage\x18\x06 \x01(\tR\amessage\x12\x12\n" +
	"\x04icon\x18\a \x01(\x05R\x04icon\x12\x14\n" +
	"\x05level\x18\b \x01(\x05R\x05level\x12\x1e\n" +
	"\n" +
	"prosperity\x18\t \x01(\x05R\n" +
	"prosperity\"Q\n" +
	"\x0eS2CLoginFinish\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"z\n" +
	"\x13C2SPlayerModifyInfo\x12\x12\n" +
	"\x04nick\x18\x01 \x02(\tR\x04nick\x12\x16\n" +
	"\x06avatar\x18\x02 \x03(\x05R\x06avatar\x12#\n" +
	"\x06gender\x18\x03 \x02(\x0e2\v.pb.EGenderR\x06gender\x12\x12\n" +
	"\x04icon\x18\x04 \x02(\x05R\x04icon\"L\n" +
	"\x14S2CPlayerModifyLevel\x12\x14\n" +
	"\x05level\x18\x01 \x01(\x05R\x05level\x12\x1e\n" +
	"\n" +
	"prosperity\x18\x02 \x01(\x05R\n" +
	"prosperity\";\n" +
	"\x19S2CPlayerProsperityUpdate\x12\x1e\n" +
	"\n" +
	"prosperity\x18\x02 \x01(\x05R\n" +
	"prosperity*z\n" +
	"\vdevice_type\x12\v\n" +
	"\aunknown\x10\x00\x12\n" +
	"\n" +
	"\x06editor\x10\x01\x12\v\n" +
	"\aandroid\x10\x02\x12\a\n" +
	"\x03iOS\x10\x04\x12\x06\n" +
	"\x02pc\x10\x05\x12\t\n" +
	"\x05macOS\x10\x06\x12\t\n" +
	"\x05linux\x10\a\x12\t\n" +
	"\x05webGL\x10\b\x12\x06\n" +
	"\x02PS\x10\t\x12\v\n" +
	"\aXboxOne\x10\n" +
	"B&Z$kairo_paradise_server/services/pb;pb"

var (
	file__01_game_proto_rawDescOnce sync.Once
	file__01_game_proto_rawDescData []byte
)

func file__01_game_proto_rawDescGZIP() []byte {
	file__01_game_proto_rawDescOnce.Do(func() {
		file__01_game_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file__01_game_proto_rawDesc), len(file__01_game_proto_rawDesc)))
	})
	return file__01_game_proto_rawDescData
}

var file__01_game_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file__01_game_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file__01_game_proto_goTypes = []any{
	(DeviceType)(0),                   // 0: pb.device_type
	(*S2CCommResponse)(nil),           // 1: pb.S2CCommResponse
	(*C2SEnterGame)(nil),              // 2: pb.C2SEnterGame
	(*S2CEnterGame)(nil),              // 3: pb.S2CEnterGame
	(*S2CPlayerInfo)(nil),             // 4: pb.S2CPlayerInfo
	(*S2CLoginFinish)(nil),            // 5: pb.S2CLoginFinish
	(*C2SPlayerModifyInfo)(nil),       // 6: pb.C2SPlayerModifyInfo
	(*S2CPlayerModifyLevel)(nil),      // 7: pb.S2CPlayerModifyLevel
	(*S2CPlayerProsperityUpdate)(nil), // 8: pb.S2CPlayerProsperityUpdate
	(ResponseCode)(0),                 // 9: pb.response_code
	(EGender)(0),                      // 10: pb.EGender
}
var file__01_game_proto_depIdxs = []int32{
	9,  // 0: pb.S2CCommResponse.code:type_name -> pb.response_code
	0,  // 1: pb.C2SEnterGame.deviceType:type_name -> pb.device_type
	9,  // 2: pb.S2CEnterGame.code:type_name -> pb.response_code
	9,  // 3: pb.S2CPlayerInfo.code:type_name -> pb.response_code
	10, // 4: pb.S2CPlayerInfo.gender:type_name -> pb.EGender
	9,  // 5: pb.S2CLoginFinish.code:type_name -> pb.response_code
	10, // 6: pb.C2SPlayerModifyInfo.gender:type_name -> pb.EGender
	7,  // [7:7] is the sub-list for method output_type
	7,  // [7:7] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file__01_game_proto_init() }
func file__01_game_proto_init() {
	if File__01_game_proto != nil {
		return
	}
	file_code_proto_init()
	file_gameconfig_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file__01_game_proto_rawDesc), len(file__01_game_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file__01_game_proto_goTypes,
		DependencyIndexes: file__01_game_proto_depIdxs,
		EnumInfos:         file__01_game_proto_enumTypes,
		MessageInfos:      file__01_game_proto_msgTypes,
	}.Build()
	File__01_game_proto = out.File
	file__01_game_proto_goTypes = nil
	file__01_game_proto_depIdxs = nil
}
