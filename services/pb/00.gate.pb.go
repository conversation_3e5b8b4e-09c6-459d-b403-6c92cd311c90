// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: 00.gate.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 不返回给前端的协议
// 100
// USED
type S2GEmpty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2GEmpty) Reset() {
	*x = S2GEmpty{}
	mi := &file__00_gate_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2GEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2GEmpty) ProtoMessage() {}

func (x *S2GEmpty) ProtoReflect() protoreflect.Message {
	mi := &file__00_gate_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2GEmpty.ProtoReflect.Descriptor instead.
func (*S2GEmpty) Descriptor() ([]byte, []int) {
	return file__00_gate_proto_rawDescGZIP(), []int{0}
}

// 错误返回协议
// 999
// USED
type G2CError struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	ProtocolId    *uint32                `protobuf:"varint,3,opt,name=protocolId" json:"protocolId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *G2CError) Reset() {
	*x = G2CError{}
	mi := &file__00_gate_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *G2CError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2CError) ProtoMessage() {}

func (x *G2CError) ProtoReflect() protoreflect.Message {
	mi := &file__00_gate_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2CError.ProtoReflect.Descriptor instead.
func (*G2CError) Descriptor() ([]byte, []int) {
	return file__00_gate_proto_rawDescGZIP(), []int{1}
}

func (x *G2CError) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *G2CError) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *G2CError) GetProtocolId() uint32 {
	if x != nil && x.ProtocolId != nil {
		return *x.ProtocolId
	}
	return 0
}

// 响应协议
// 998
// USED
type S2GResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2GResponse) Reset() {
	*x = S2GResponse{}
	mi := &file__00_gate_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2GResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2GResponse) ProtoMessage() {}

func (x *S2GResponse) ProtoReflect() protoreflect.Message {
	mi := &file__00_gate_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2GResponse.ProtoReflect.Descriptor instead.
func (*S2GResponse) Descriptor() ([]byte, []int) {
	return file__00_gate_proto_rawDescGZIP(), []int{2}
}

func (x *S2GResponse) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

// 心跳包
// 1000
// USED
type C2GHeartbeat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Seq           *int32                 `protobuf:"varint,1,opt,name=seq" json:"seq,omitempty"`
	Timestamp     *int64                 `protobuf:"varint,2,req,name=timestamp" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2GHeartbeat) Reset() {
	*x = C2GHeartbeat{}
	mi := &file__00_gate_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2GHeartbeat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2GHeartbeat) ProtoMessage() {}

func (x *C2GHeartbeat) ProtoReflect() protoreflect.Message {
	mi := &file__00_gate_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2GHeartbeat.ProtoReflect.Descriptor instead.
func (*C2GHeartbeat) Descriptor() ([]byte, []int) {
	return file__00_gate_proto_rawDescGZIP(), []int{3}
}

func (x *C2GHeartbeat) GetSeq() int32 {
	if x != nil && x.Seq != nil {
		return *x.Seq
	}
	return 0
}

func (x *C2GHeartbeat) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

// 心跳包
// 1001
// USED
type G2CHeartbeat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Seq           *int32                 `protobuf:"varint,1,opt,name=seq" json:"seq,omitempty"`
	Timestamp     *int64                 `protobuf:"varint,2,req,name=timestamp" json:"timestamp,omitempty"` // 时间戳毫秒
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *G2CHeartbeat) Reset() {
	*x = G2CHeartbeat{}
	mi := &file__00_gate_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *G2CHeartbeat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2CHeartbeat) ProtoMessage() {}

func (x *G2CHeartbeat) ProtoReflect() protoreflect.Message {
	mi := &file__00_gate_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2CHeartbeat.ProtoReflect.Descriptor instead.
func (*G2CHeartbeat) Descriptor() ([]byte, []int) {
	return file__00_gate_proto_rawDescGZIP(), []int{4}
}

func (x *G2CHeartbeat) GetSeq() int32 {
	if x != nil && x.Seq != nil {
		return *x.Seq
	}
	return 0
}

func (x *G2CHeartbeat) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

// 初始化服务器状态，前端不需要处理
// 1002
// USED
type G2SInit struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Services      *string                `protobuf:"bytes,1,req,name=services" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *G2SInit) Reset() {
	*x = G2SInit{}
	mi := &file__00_gate_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *G2SInit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2SInit) ProtoMessage() {}

func (x *G2SInit) ProtoReflect() protoreflect.Message {
	mi := &file__00_gate_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2SInit.ProtoReflect.Descriptor instead.
func (*G2SInit) Descriptor() ([]byte, []int) {
	return file__00_gate_proto_rawDescGZIP(), []int{5}
}

func (x *G2SInit) GetServices() string {
	if x != nil && x.Services != nil {
		return *x.Services
	}
	return ""
}

// 连接断开通知
// 1003
// USED
type G2SDisconnect struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *G2SDisconnect) Reset() {
	*x = G2SDisconnect{}
	mi := &file__00_gate_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *G2SDisconnect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2SDisconnect) ProtoMessage() {}

func (x *G2SDisconnect) ProtoReflect() protoreflect.Message {
	mi := &file__00_gate_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2SDisconnect.ProtoReflect.Descriptor instead.
func (*G2SDisconnect) Descriptor() ([]byte, []int) {
	return file__00_gate_proto_rawDescGZIP(), []int{6}
}

var File__00_gate_proto protoreflect.FileDescriptor

const file__00_gate_proto_rawDesc = "" +
	"\n" +
	"\r00.gate.proto\x12\x02pb\x1a\n" +
	"code.proto\"\n" +
	"\n" +
	"\bS2GEmpty\"k\n" +
	"\bG2CError\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1e\n" +
	"\n" +
	"protocolId\x18\x03 \x01(\rR\n" +
	"protocolId\"4\n" +
	"\vS2GResponse\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\">\n" +
	"\fC2GHeartbeat\x12\x10\n" +
	"\x03seq\x18\x01 \x01(\x05R\x03seq\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x02(\x03R\ttimestamp\">\n" +
	"\fG2CHeartbeat\x12\x10\n" +
	"\x03seq\x18\x01 \x01(\x05R\x03seq\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x02(\x03R\ttimestamp\"%\n" +
	"\aG2SInit\x12\x1a\n" +
	"\bservices\x18\x01 \x02(\tR\bservices\"\x0f\n" +
	"\rG2SDisconnectB&Z$kairo_paradise_server/services/pb;pb"

var (
	file__00_gate_proto_rawDescOnce sync.Once
	file__00_gate_proto_rawDescData []byte
)

func file__00_gate_proto_rawDescGZIP() []byte {
	file__00_gate_proto_rawDescOnce.Do(func() {
		file__00_gate_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file__00_gate_proto_rawDesc), len(file__00_gate_proto_rawDesc)))
	})
	return file__00_gate_proto_rawDescData
}

var file__00_gate_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file__00_gate_proto_goTypes = []any{
	(*S2GEmpty)(nil),      // 0: pb.S2GEmpty
	(*G2CError)(nil),      // 1: pb.G2CError
	(*S2GResponse)(nil),   // 2: pb.S2GResponse
	(*C2GHeartbeat)(nil),  // 3: pb.C2GHeartbeat
	(*G2CHeartbeat)(nil),  // 4: pb.G2CHeartbeat
	(*G2SInit)(nil),       // 5: pb.G2SInit
	(*G2SDisconnect)(nil), // 6: pb.G2SDisconnect
	(ResponseCode)(0),     // 7: pb.response_code
}
var file__00_gate_proto_depIdxs = []int32{
	7, // 0: pb.G2CError.code:type_name -> pb.response_code
	7, // 1: pb.S2GResponse.code:type_name -> pb.response_code
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file__00_gate_proto_init() }
func file__00_gate_proto_init() {
	if File__00_gate_proto != nil {
		return
	}
	file_code_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file__00_gate_proto_rawDesc), len(file__00_gate_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file__00_gate_proto_goTypes,
		DependencyIndexes: file__00_gate_proto_depIdxs,
		MessageInfos:      file__00_gate_proto_msgTypes,
	}.Build()
	File__00_gate_proto = out.File
	file__00_gate_proto_goTypes = nil
	file__00_gate_proto_depIdxs = nil
}
