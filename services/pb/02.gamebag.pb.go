// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: 02.gamebag.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BagItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,1,req,name=Id" json:"Id,omitempty"`
	Count         *int32                 `protobuf:"varint,2,req,name=Count" json:"Count,omitempty"`
	ItemId        *int32                 `protobuf:"varint,3,req,name=ItemId" json:"ItemId,omitempty"`           // 物品配置ID
	CreateTime    *int64                 `protobuf:"varint,4,req,name=CreateTime" json:"CreateTime,omitempty"`   // 创建时间戳
	ExpireTime    *int64                 `protobuf:"varint,5,req,name=ExpireTime" json:"ExpireTime,omitempty"`   // 有效期至,时间戳
	Type          *EItem                 `protobuf:"varint,6,req,name=Type,enum=pb.EItem" json:"Type,omitempty"` // 类型，便于分类
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BagItem) Reset() {
	*x = BagItem{}
	mi := &file__02_gamebag_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BagItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BagItem) ProtoMessage() {}

func (x *BagItem) ProtoReflect() protoreflect.Message {
	mi := &file__02_gamebag_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BagItem.ProtoReflect.Descriptor instead.
func (*BagItem) Descriptor() ([]byte, []int) {
	return file__02_gamebag_proto_rawDescGZIP(), []int{0}
}

func (x *BagItem) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *BagItem) GetCount() int32 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

func (x *BagItem) GetItemId() int32 {
	if x != nil && x.ItemId != nil {
		return *x.ItemId
	}
	return 0
}

func (x *BagItem) GetCreateTime() int64 {
	if x != nil && x.CreateTime != nil {
		return *x.CreateTime
	}
	return 0
}

func (x *BagItem) GetExpireTime() int64 {
	if x != nil && x.ExpireTime != nil {
		return *x.ExpireTime
	}
	return 0
}

func (x *BagItem) GetType() EItem {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EItem_EItem_Money
}

type ListBagItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*BagItem             `protobuf:"bytes,1,rep,name=Items" json:"Items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListBagItem) Reset() {
	*x = ListBagItem{}
	mi := &file__02_gamebag_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListBagItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBagItem) ProtoMessage() {}

func (x *ListBagItem) ProtoReflect() protoreflect.Message {
	mi := &file__02_gamebag_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBagItem.ProtoReflect.Descriptor instead.
func (*ListBagItem) Descriptor() ([]byte, []int) {
	return file__02_gamebag_proto_rawDescGZIP(), []int{1}
}

func (x *ListBagItem) GetItems() []*BagItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// 背包协议
// 10100
// USED
type S2CBackpackInit struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Coin          *int32                 `protobuf:"varint,1,req,name=coin" json:"coin,omitempty"` // 钻石货币
	ItemList      []*BagItem             `protobuf:"bytes,2,rep,name=itemList" json:"itemList,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CBackpackInit) Reset() {
	*x = S2CBackpackInit{}
	mi := &file__02_gamebag_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CBackpackInit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CBackpackInit) ProtoMessage() {}

func (x *S2CBackpackInit) ProtoReflect() protoreflect.Message {
	mi := &file__02_gamebag_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CBackpackInit.ProtoReflect.Descriptor instead.
func (*S2CBackpackInit) Descriptor() ([]byte, []int) {
	return file__02_gamebag_proto_rawDescGZIP(), []int{2}
}

func (x *S2CBackpackInit) GetCoin() int32 {
	if x != nil && x.Coin != nil {
		return *x.Coin
	}
	return 0
}

func (x *S2CBackpackInit) GetItemList() []*BagItem {
	if x != nil {
		return x.ItemList
	}
	return nil
}

// 更新背包货币
// 10101
// USED
type S2CBackpackUpdateCoin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Coin          *int32                 `protobuf:"varint,1,req,name=coin" json:"coin,omitempty"` // 钻石货币
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CBackpackUpdateCoin) Reset() {
	*x = S2CBackpackUpdateCoin{}
	mi := &file__02_gamebag_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CBackpackUpdateCoin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CBackpackUpdateCoin) ProtoMessage() {}

func (x *S2CBackpackUpdateCoin) ProtoReflect() protoreflect.Message {
	mi := &file__02_gamebag_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CBackpackUpdateCoin.ProtoReflect.Descriptor instead.
func (*S2CBackpackUpdateCoin) Descriptor() ([]byte, []int) {
	return file__02_gamebag_proto_rawDescGZIP(), []int{3}
}

func (x *S2CBackpackUpdateCoin) GetCoin() int32 {
	if x != nil && x.Coin != nil {
		return *x.Coin
	}
	return 0
}

// 背包增加物品
// 10102
// USED
type S2CBackpackAddItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ModifyList    []*BagItem             `protobuf:"bytes,1,rep,name=modifyList" json:"modifyList,omitempty"`
	AddType       *EResourceAddType      `protobuf:"varint,2,req,name=addType,enum=pb.EResourceAddType" json:"addType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CBackpackAddItem) Reset() {
	*x = S2CBackpackAddItem{}
	mi := &file__02_gamebag_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CBackpackAddItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CBackpackAddItem) ProtoMessage() {}

func (x *S2CBackpackAddItem) ProtoReflect() protoreflect.Message {
	mi := &file__02_gamebag_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CBackpackAddItem.ProtoReflect.Descriptor instead.
func (*S2CBackpackAddItem) Descriptor() ([]byte, []int) {
	return file__02_gamebag_proto_rawDescGZIP(), []int{4}
}

func (x *S2CBackpackAddItem) GetModifyList() []*BagItem {
	if x != nil {
		return x.ModifyList
	}
	return nil
}

func (x *S2CBackpackAddItem) GetAddType() EResourceAddType {
	if x != nil && x.AddType != nil {
		return *x.AddType
	}
	return EResourceAddType_EResourceAddType_None
}

// 背包删除物品
// 10103
// USED
type S2CBackpackSubItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ModifyList    []*BagItem             `protobuf:"bytes,1,rep,name=modifyList" json:"modifyList,omitempty"`
	SubType       *EResourceSubType      `protobuf:"varint,2,req,name=subType,enum=pb.EResourceSubType" json:"subType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CBackpackSubItem) Reset() {
	*x = S2CBackpackSubItem{}
	mi := &file__02_gamebag_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CBackpackSubItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CBackpackSubItem) ProtoMessage() {}

func (x *S2CBackpackSubItem) ProtoReflect() protoreflect.Message {
	mi := &file__02_gamebag_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CBackpackSubItem.ProtoReflect.Descriptor instead.
func (*S2CBackpackSubItem) Descriptor() ([]byte, []int) {
	return file__02_gamebag_proto_rawDescGZIP(), []int{5}
}

func (x *S2CBackpackSubItem) GetModifyList() []*BagItem {
	if x != nil {
		return x.ModifyList
	}
	return nil
}

func (x *S2CBackpackSubItem) GetSubType() EResourceSubType {
	if x != nil && x.SubType != nil {
		return *x.SubType
	}
	return EResourceSubType_EResourceSubType_None
}

// 使用背包物品
// 10104
// USED
type C2SBackpackUseItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UseItem       *BagItem               `protobuf:"bytes,1,req,name=useItem" json:"useItem,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SBackpackUseItem) Reset() {
	*x = C2SBackpackUseItem{}
	mi := &file__02_gamebag_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SBackpackUseItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SBackpackUseItem) ProtoMessage() {}

func (x *C2SBackpackUseItem) ProtoReflect() protoreflect.Message {
	mi := &file__02_gamebag_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SBackpackUseItem.ProtoReflect.Descriptor instead.
func (*C2SBackpackUseItem) Descriptor() ([]byte, []int) {
	return file__02_gamebag_proto_rawDescGZIP(), []int{6}
}

func (x *C2SBackpackUseItem) GetUseItem() *BagItem {
	if x != nil {
		return x.UseItem
	}
	return nil
}

var File__02_gamebag_proto protoreflect.FileDescriptor

const file__02_gamebag_proto_rawDesc = "" +
	"\n" +
	"\x1002.gamebag.proto\x12\x02pb\x1a\x10gameconfig.proto\"\xa6\x01\n" +
	"\aBagItem\x12\x0e\n" +
	"\x02Id\x18\x01 \x02(\x05R\x02Id\x12\x14\n" +
	"\x05Count\x18\x02 \x02(\x05R\x05Count\x12\x16\n" +
	"\x06ItemId\x18\x03 \x02(\x05R\x06ItemId\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\x04 \x02(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"ExpireTime\x18\x05 \x02(\x03R\n" +
	"ExpireTime\x12\x1d\n" +
	"\x04Type\x18\x06 \x02(\x0e2\t.pb.EItemR\x04Type\"0\n" +
	"\vListBagItem\x12!\n" +
	"\x05Items\x18\x01 \x03(\v2\v.pb.BagItemR\x05Items\"N\n" +
	"\x0fS2CBackpackInit\x12\x12\n" +
	"\x04coin\x18\x01 \x02(\x05R\x04coin\x12'\n" +
	"\bitemList\x18\x02 \x03(\v2\v.pb.BagItemR\bitemList\"+\n" +
	"\x15S2CBackpackUpdateCoin\x12\x12\n" +
	"\x04coin\x18\x01 \x02(\x05R\x04coin\"q\n" +
	"\x12S2CBackpackAddItem\x12+\n" +
	"\n" +
	"modifyList\x18\x01 \x03(\v2\v.pb.BagItemR\n" +
	"modifyList\x12.\n" +
	"\aaddType\x18\x02 \x02(\x0e2\x14.pb.EResourceAddTypeR\aaddType\"q\n" +
	"\x12S2CBackpackSubItem\x12+\n" +
	"\n" +
	"modifyList\x18\x01 \x03(\v2\v.pb.BagItemR\n" +
	"modifyList\x12.\n" +
	"\asubType\x18\x02 \x02(\x0e2\x14.pb.EResourceSubTypeR\asubType\";\n" +
	"\x12C2SBackpackUseItem\x12%\n" +
	"\auseItem\x18\x01 \x02(\v2\v.pb.BagItemR\auseItemB&Z$kairo_paradise_server/services/pb;pb"

var (
	file__02_gamebag_proto_rawDescOnce sync.Once
	file__02_gamebag_proto_rawDescData []byte
)

func file__02_gamebag_proto_rawDescGZIP() []byte {
	file__02_gamebag_proto_rawDescOnce.Do(func() {
		file__02_gamebag_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file__02_gamebag_proto_rawDesc), len(file__02_gamebag_proto_rawDesc)))
	})
	return file__02_gamebag_proto_rawDescData
}

var file__02_gamebag_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file__02_gamebag_proto_goTypes = []any{
	(*BagItem)(nil),               // 0: pb.BagItem
	(*ListBagItem)(nil),           // 1: pb.ListBagItem
	(*S2CBackpackInit)(nil),       // 2: pb.S2CBackpackInit
	(*S2CBackpackUpdateCoin)(nil), // 3: pb.S2CBackpackUpdateCoin
	(*S2CBackpackAddItem)(nil),    // 4: pb.S2CBackpackAddItem
	(*S2CBackpackSubItem)(nil),    // 5: pb.S2CBackpackSubItem
	(*C2SBackpackUseItem)(nil),    // 6: pb.C2SBackpackUseItem
	(EItem)(0),                    // 7: pb.EItem
	(EResourceAddType)(0),         // 8: pb.EResourceAddType
	(EResourceSubType)(0),         // 9: pb.EResourceSubType
}
var file__02_gamebag_proto_depIdxs = []int32{
	7, // 0: pb.BagItem.Type:type_name -> pb.EItem
	0, // 1: pb.ListBagItem.Items:type_name -> pb.BagItem
	0, // 2: pb.S2CBackpackInit.itemList:type_name -> pb.BagItem
	0, // 3: pb.S2CBackpackAddItem.modifyList:type_name -> pb.BagItem
	8, // 4: pb.S2CBackpackAddItem.addType:type_name -> pb.EResourceAddType
	0, // 5: pb.S2CBackpackSubItem.modifyList:type_name -> pb.BagItem
	9, // 6: pb.S2CBackpackSubItem.subType:type_name -> pb.EResourceSubType
	0, // 7: pb.C2SBackpackUseItem.useItem:type_name -> pb.BagItem
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file__02_gamebag_proto_init() }
func file__02_gamebag_proto_init() {
	if File__02_gamebag_proto != nil {
		return
	}
	file_gameconfig_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file__02_gamebag_proto_rawDesc), len(file__02_gamebag_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file__02_gamebag_proto_goTypes,
		DependencyIndexes: file__02_gamebag_proto_depIdxs,
		MessageInfos:      file__02_gamebag_proto_msgTypes,
	}.Build()
	File__02_gamebag_proto = out.File
	file__02_gamebag_proto_goTypes = nil
	file__02_gamebag_proto_depIdxs = nil
}
