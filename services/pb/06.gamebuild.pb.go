// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: 06.gamebuild.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 建筑数据
type BuildData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	ItemId        *int32                 `protobuf:"varint,2,req,name=itemId" json:"itemId,omitempty"`
	Vector        *Vector2               `protobuf:"bytes,3,req,name=vector" json:"vector,omitempty"`
	Direction     *int32                 `protobuf:"varint,4,req,name=direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BuildData) Reset() {
	*x = BuildData{}
	mi := &file__06_gamebuild_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BuildData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildData) ProtoMessage() {}

func (x *BuildData) ProtoReflect() protoreflect.Message {
	mi := &file__06_gamebuild_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildData.ProtoReflect.Descriptor instead.
func (*BuildData) Descriptor() ([]byte, []int) {
	return file__06_gamebuild_proto_rawDescGZIP(), []int{0}
}

func (x *BuildData) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *BuildData) GetItemId() int32 {
	if x != nil && x.ItemId != nil {
		return *x.ItemId
	}
	return 0
}

func (x *BuildData) GetVector() *Vector2 {
	if x != nil {
		return x.Vector
	}
	return nil
}

func (x *BuildData) GetDirection() int32 {
	if x != nil && x.Direction != nil {
		return *x.Direction
	}
	return 0
}

type ListBuildData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Builds        []*BuildData           `protobuf:"bytes,1,rep,name=builds" json:"builds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListBuildData) Reset() {
	*x = ListBuildData{}
	mi := &file__06_gamebuild_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListBuildData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBuildData) ProtoMessage() {}

func (x *ListBuildData) ProtoReflect() protoreflect.Message {
	mi := &file__06_gamebuild_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBuildData.ProtoReflect.Descriptor instead.
func (*ListBuildData) Descriptor() ([]byte, []int) {
	return file__06_gamebuild_proto_rawDescGZIP(), []int{1}
}

func (x *ListBuildData) GetBuilds() []*BuildData {
	if x != nil {
		return x.Builds
	}
	return nil
}

// 建筑数据初始化
// 10260
// USED
type S2CBuildDataInit struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Builds        []*BuildData           `protobuf:"bytes,1,rep,name=builds" json:"builds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CBuildDataInit) Reset() {
	*x = S2CBuildDataInit{}
	mi := &file__06_gamebuild_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CBuildDataInit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CBuildDataInit) ProtoMessage() {}

func (x *S2CBuildDataInit) ProtoReflect() protoreflect.Message {
	mi := &file__06_gamebuild_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CBuildDataInit.ProtoReflect.Descriptor instead.
func (*S2CBuildDataInit) Descriptor() ([]byte, []int) {
	return file__06_gamebuild_proto_rawDescGZIP(), []int{2}
}

func (x *S2CBuildDataInit) GetBuilds() []*BuildData {
	if x != nil {
		return x.Builds
	}
	return nil
}

// 保存建筑
// 10261
// USED
type C2SBuildDataSave struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Builds        []*BuildData           `protobuf:"bytes,1,rep,name=builds" json:"builds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SBuildDataSave) Reset() {
	*x = C2SBuildDataSave{}
	mi := &file__06_gamebuild_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SBuildDataSave) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SBuildDataSave) ProtoMessage() {}

func (x *C2SBuildDataSave) ProtoReflect() protoreflect.Message {
	mi := &file__06_gamebuild_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SBuildDataSave.ProtoReflect.Descriptor instead.
func (*C2SBuildDataSave) Descriptor() ([]byte, []int) {
	return file__06_gamebuild_proto_rawDescGZIP(), []int{3}
}

func (x *C2SBuildDataSave) GetBuilds() []*BuildData {
	if x != nil {
		return x.Builds
	}
	return nil
}

// 清空建筑
// 10262
// USED
type C2SClearBuild struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SClearBuild) Reset() {
	*x = C2SClearBuild{}
	mi := &file__06_gamebuild_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SClearBuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SClearBuild) ProtoMessage() {}

func (x *C2SClearBuild) ProtoReflect() protoreflect.Message {
	mi := &file__06_gamebuild_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SClearBuild.ProtoReflect.Descriptor instead.
func (*C2SClearBuild) Descriptor() ([]byte, []int) {
	return file__06_gamebuild_proto_rawDescGZIP(), []int{4}
}

var File__06_gamebuild_proto protoreflect.FileDescriptor

const file__06_gamebuild_proto_rawDesc = "" +
	"\n" +
	"\x1206.gamebuild.proto\x12\x02pb\x1a\x10gameconfig.proto\"v\n" +
	"\tBuildData\x12\x0e\n" +
	"\x02id\x18\x01 \x02(\x05R\x02id\x12\x16\n" +
	"\x06itemId\x18\x02 \x02(\x05R\x06itemId\x12#\n" +
	"\x06vector\x18\x03 \x02(\v2\v.pb.Vector2R\x06vector\x12\x1c\n" +
	"\tdirection\x18\x04 \x02(\x05R\tdirection\"6\n" +
	"\rListBuildData\x12%\n" +
	"\x06builds\x18\x01 \x03(\v2\r.pb.BuildDataR\x06builds\"9\n" +
	"\x10S2CBuildDataInit\x12%\n" +
	"\x06builds\x18\x01 \x03(\v2\r.pb.BuildDataR\x06builds\"9\n" +
	"\x10C2SBuildDataSave\x12%\n" +
	"\x06builds\x18\x01 \x03(\v2\r.pb.BuildDataR\x06builds\"\x0f\n" +
	"\rC2SClearBuildB&Z$kairo_paradise_server/services/pb;pb"

var (
	file__06_gamebuild_proto_rawDescOnce sync.Once
	file__06_gamebuild_proto_rawDescData []byte
)

func file__06_gamebuild_proto_rawDescGZIP() []byte {
	file__06_gamebuild_proto_rawDescOnce.Do(func() {
		file__06_gamebuild_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file__06_gamebuild_proto_rawDesc), len(file__06_gamebuild_proto_rawDesc)))
	})
	return file__06_gamebuild_proto_rawDescData
}

var file__06_gamebuild_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file__06_gamebuild_proto_goTypes = []any{
	(*BuildData)(nil),        // 0: pb.BuildData
	(*ListBuildData)(nil),    // 1: pb.ListBuildData
	(*S2CBuildDataInit)(nil), // 2: pb.S2CBuildDataInit
	(*C2SBuildDataSave)(nil), // 3: pb.C2SBuildDataSave
	(*C2SClearBuild)(nil),    // 4: pb.C2SClearBuild
	(*Vector2)(nil),          // 5: pb.Vector2
}
var file__06_gamebuild_proto_depIdxs = []int32{
	5, // 0: pb.BuildData.vector:type_name -> pb.Vector2
	0, // 1: pb.ListBuildData.builds:type_name -> pb.BuildData
	0, // 2: pb.S2CBuildDataInit.builds:type_name -> pb.BuildData
	0, // 3: pb.C2SBuildDataSave.builds:type_name -> pb.BuildData
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file__06_gamebuild_proto_init() }
func file__06_gamebuild_proto_init() {
	if File__06_gamebuild_proto != nil {
		return
	}
	file_gameconfig_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file__06_gamebuild_proto_rawDesc), len(file__06_gamebuild_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file__06_gamebuild_proto_goTypes,
		DependencyIndexes: file__06_gamebuild_proto_depIdxs,
		MessageInfos:      file__06_gamebuild_proto_msgTypes,
	}.Build()
	File__06_gamebuild_proto = out.File
	file__06_gamebuild_proto_goTypes = nil
	file__06_gamebuild_proto_depIdxs = nil
}
