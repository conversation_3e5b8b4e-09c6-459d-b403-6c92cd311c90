// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: 3001.pubicrank.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 角色外观
type PlayerAppearance struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerId      *uint64                `protobuf:"varint,1,req,name=player_id,json=playerId" json:"player_id,omitempty"`      // 玩家ID
	PlayerName    *string                `protobuf:"bytes,2,opt,name=player_name,json=playerName" json:"player_name,omitempty"` // 玩家名称
	Level         *int32                 `protobuf:"varint,3,opt,name=level" json:"level,omitempty"`                            // 玩家等级
	Prosperity    *int32                 `protobuf:"varint,4,opt,name=prosperity" json:"prosperity,omitempty"`                  // 繁荣度
	Icon          *int32                 `protobuf:"varint,5,opt,name=icon" json:"icon,omitempty"`                              // 玩家头像
	Gender        *int32                 `protobuf:"varint,6,opt,name=gender" json:"gender,omitempty"`                          // 性别
	Avatars       []int32                `protobuf:"varint,7,rep,name=avatars" json:"avatars,omitempty"`                        // 外观列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerAppearance) Reset() {
	*x = PlayerAppearance{}
	mi := &file__3001_pubicrank_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerAppearance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerAppearance) ProtoMessage() {}

func (x *PlayerAppearance) ProtoReflect() protoreflect.Message {
	mi := &file__3001_pubicrank_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerAppearance.ProtoReflect.Descriptor instead.
func (*PlayerAppearance) Descriptor() ([]byte, []int) {
	return file__3001_pubicrank_proto_rawDescGZIP(), []int{0}
}

func (x *PlayerAppearance) GetPlayerId() uint64 {
	if x != nil && x.PlayerId != nil {
		return *x.PlayerId
	}
	return 0
}

func (x *PlayerAppearance) GetPlayerName() string {
	if x != nil && x.PlayerName != nil {
		return *x.PlayerName
	}
	return ""
}

func (x *PlayerAppearance) GetLevel() int32 {
	if x != nil && x.Level != nil {
		return *x.Level
	}
	return 0
}

func (x *PlayerAppearance) GetProsperity() int32 {
	if x != nil && x.Prosperity != nil {
		return *x.Prosperity
	}
	return 0
}

func (x *PlayerAppearance) GetIcon() int32 {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return 0
}

func (x *PlayerAppearance) GetGender() int32 {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return 0
}

func (x *PlayerAppearance) GetAvatars() []int32 {
	if x != nil {
		return x.Avatars
	}
	return nil
}

// 排行榜数据
type RankEntryData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fixme: 通用模块
	Appearance    *PlayerAppearance `protobuf:"bytes,1,req,name=appearance" json:"appearance,omitempty"` // 玩家外观信息
	Score         *int32            `protobuf:"varint,2,req,name=score" json:"score,omitempty"`          // 分数
	Ranking       *int32            `protobuf:"varint,3,req,name=ranking" json:"ranking,omitempty"`      // 排行
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RankEntryData) Reset() {
	*x = RankEntryData{}
	mi := &file__3001_pubicrank_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RankEntryData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankEntryData) ProtoMessage() {}

func (x *RankEntryData) ProtoReflect() protoreflect.Message {
	mi := &file__3001_pubicrank_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankEntryData.ProtoReflect.Descriptor instead.
func (*RankEntryData) Descriptor() ([]byte, []int) {
	return file__3001_pubicrank_proto_rawDescGZIP(), []int{1}
}

func (x *RankEntryData) GetAppearance() *PlayerAppearance {
	if x != nil {
		return x.Appearance
	}
	return nil
}

func (x *RankEntryData) GetScore() int32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *RankEntryData) GetRanking() int32 {
	if x != nil && x.Ranking != nil {
		return *x.Ranking
	}
	return 0
}

// 更新排行榜数据请求
// 31000
// USED
type G2SUpdateRank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RankType      *ERankType             `protobuf:"varint,1,req,name=rank_type,json=rankType,enum=pb.ERankType" json:"rank_type,omitempty"` // 排行榜类型
	Entry         *RankEntryData         `protobuf:"bytes,2,req,name=entry" json:"entry,omitempty"`                                          // 排行榜条目数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *G2SUpdateRank) Reset() {
	*x = G2SUpdateRank{}
	mi := &file__3001_pubicrank_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *G2SUpdateRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2SUpdateRank) ProtoMessage() {}

func (x *G2SUpdateRank) ProtoReflect() protoreflect.Message {
	mi := &file__3001_pubicrank_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2SUpdateRank.ProtoReflect.Descriptor instead.
func (*G2SUpdateRank) Descriptor() ([]byte, []int) {
	return file__3001_pubicrank_proto_rawDescGZIP(), []int{2}
}

func (x *G2SUpdateRank) GetRankType() ERankType {
	if x != nil && x.RankType != nil {
		return *x.RankType
	}
	return ERankType_ERankType_None
}

func (x *G2SUpdateRank) GetEntry() *RankEntryData {
	if x != nil {
		return x.Entry
	}
	return nil
}

// 获取排行榜数据请求
// 31001
// USED
type C2SGetRankList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RankType      *ERankType             `protobuf:"varint,1,req,name=rank_type,json=rankType,enum=pb.ERankType" json:"rank_type,omitempty"` // 排行榜类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SGetRankList) Reset() {
	*x = C2SGetRankList{}
	mi := &file__3001_pubicrank_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SGetRankList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SGetRankList) ProtoMessage() {}

func (x *C2SGetRankList) ProtoReflect() protoreflect.Message {
	mi := &file__3001_pubicrank_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SGetRankList.ProtoReflect.Descriptor instead.
func (*C2SGetRankList) Descriptor() ([]byte, []int) {
	return file__3001_pubicrank_proto_rawDescGZIP(), []int{3}
}

func (x *C2SGetRankList) GetRankType() ERankType {
	if x != nil && x.RankType != nil {
		return *x.RankType
	}
	return ERankType_ERankType_None
}

// 获取排行榜数据响应
// 31002
// USED
type S2CGetRankList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"`          // 响应码
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`                          // 错误信息
	Entries       []*RankEntryData       `protobuf:"bytes,3,rep,name=entries" json:"entries,omitempty"`                          // 排行榜数据
	TotalCount    *int32                 `protobuf:"varint,4,opt,name=total_count,json=totalCount" json:"total_count,omitempty"` // 排行榜总条目数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CGetRankList) Reset() {
	*x = S2CGetRankList{}
	mi := &file__3001_pubicrank_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CGetRankList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CGetRankList) ProtoMessage() {}

func (x *S2CGetRankList) ProtoReflect() protoreflect.Message {
	mi := &file__3001_pubicrank_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CGetRankList.ProtoReflect.Descriptor instead.
func (*S2CGetRankList) Descriptor() ([]byte, []int) {
	return file__3001_pubicrank_proto_rawDescGZIP(), []int{4}
}

func (x *S2CGetRankList) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CGetRankList) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CGetRankList) GetEntries() []*RankEntryData {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *S2CGetRankList) GetTotalCount() int32 {
	if x != nil && x.TotalCount != nil {
		return *x.TotalCount
	}
	return 0
}

// 获取指定玩家排名请求
// 31003
// USED
type C2SGetPlayerRank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RankType      *ERankType             `protobuf:"varint,1,req,name=rank_type,json=rankType,enum=pb.ERankType" json:"rank_type,omitempty"` // 排行榜类型
	PlayerId      *uint64                `protobuf:"varint,2,req,name=player_id,json=playerId" json:"player_id,omitempty"`                   // 玩家ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SGetPlayerRank) Reset() {
	*x = C2SGetPlayerRank{}
	mi := &file__3001_pubicrank_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SGetPlayerRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SGetPlayerRank) ProtoMessage() {}

func (x *C2SGetPlayerRank) ProtoReflect() protoreflect.Message {
	mi := &file__3001_pubicrank_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SGetPlayerRank.ProtoReflect.Descriptor instead.
func (*C2SGetPlayerRank) Descriptor() ([]byte, []int) {
	return file__3001_pubicrank_proto_rawDescGZIP(), []int{5}
}

func (x *C2SGetPlayerRank) GetRankType() ERankType {
	if x != nil && x.RankType != nil {
		return *x.RankType
	}
	return ERankType_ERankType_None
}

func (x *C2SGetPlayerRank) GetPlayerId() uint64 {
	if x != nil && x.PlayerId != nil {
		return *x.PlayerId
	}
	return 0
}

// 获取玩家排名响应
// 31004
// USED
type S2CGetPlayerRank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"` // 响应码
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`                 // 错误信息
	Entry         *RankEntryData         `protobuf:"bytes,3,opt,name=entry" json:"entry,omitempty"`                     // 玩家排行榜数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CGetPlayerRank) Reset() {
	*x = S2CGetPlayerRank{}
	mi := &file__3001_pubicrank_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CGetPlayerRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CGetPlayerRank) ProtoMessage() {}

func (x *S2CGetPlayerRank) ProtoReflect() protoreflect.Message {
	mi := &file__3001_pubicrank_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CGetPlayerRank.ProtoReflect.Descriptor instead.
func (*S2CGetPlayerRank) Descriptor() ([]byte, []int) {
	return file__3001_pubicrank_proto_rawDescGZIP(), []int{6}
}

func (x *S2CGetPlayerRank) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CGetPlayerRank) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CGetPlayerRank) GetEntry() *RankEntryData {
	if x != nil {
		return x.Entry
	}
	return nil
}

var File__3001_pubicrank_proto protoreflect.FileDescriptor

const file__3001_pubicrank_proto_rawDesc = "" +
	"\n" +
	"\x143001.pubicrank.proto\x12\x02pb\x1a\n" +
	"code.proto\x1a\x10gameconfig.proto\"\xcc\x01\n" +
	"\x10PlayerAppearance\x12\x1b\n" +
	"\tplayer_id\x18\x01 \x02(\x04R\bplayerId\x12\x1f\n" +
	"\vplayer_name\x18\x02 \x01(\tR\n" +
	"playerName\x12\x14\n" +
	"\x05level\x18\x03 \x01(\x05R\x05level\x12\x1e\n" +
	"\n" +
	"prosperity\x18\x04 \x01(\x05R\n" +
	"prosperity\x12\x12\n" +
	"\x04icon\x18\x05 \x01(\x05R\x04icon\x12\x16\n" +
	"\x06gender\x18\x06 \x01(\x05R\x06gender\x12\x18\n" +
	"\aavatars\x18\a \x03(\x05R\aavatars\"u\n" +
	"\rRankEntryData\x124\n" +
	"\n" +
	"appearance\x18\x01 \x02(\v2\x14.pb.PlayerAppearanceR\n" +
	"appearance\x12\x14\n" +
	"\x05score\x18\x02 \x02(\x05R\x05score\x12\x18\n" +
	"\aranking\x18\x03 \x02(\x05R\aranking\"d\n" +
	"\rG2SUpdateRank\x12*\n" +
	"\trank_type\x18\x01 \x02(\x0e2\r.pb.ERankTypeR\brankType\x12'\n" +
	"\x05entry\x18\x02 \x02(\v2\x11.pb.RankEntryDataR\x05entry\"<\n" +
	"\x0eC2SGetRankList\x12*\n" +
	"\trank_type\x18\x01 \x02(\x0e2\r.pb.ERankTypeR\brankType\"\x9f\x01\n" +
	"\x0eS2CGetRankList\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12+\n" +
	"\aentries\x18\x03 \x03(\v2\x11.pb.RankEntryDataR\aentries\x12\x1f\n" +
	"\vtotal_count\x18\x04 \x01(\x05R\n" +
	"totalCount\"[\n" +
	"\x10C2SGetPlayerRank\x12*\n" +
	"\trank_type\x18\x01 \x02(\x0e2\r.pb.ERankTypeR\brankType\x12\x1b\n" +
	"\tplayer_id\x18\x02 \x02(\x04R\bplayerId\"|\n" +
	"\x10S2CGetPlayerRank\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12'\n" +
	"\x05entry\x18\x03 \x01(\v2\x11.pb.RankEntryDataR\x05entryB&Z$kairo_paradise_server/services/pb;pb"

var (
	file__3001_pubicrank_proto_rawDescOnce sync.Once
	file__3001_pubicrank_proto_rawDescData []byte
)

func file__3001_pubicrank_proto_rawDescGZIP() []byte {
	file__3001_pubicrank_proto_rawDescOnce.Do(func() {
		file__3001_pubicrank_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file__3001_pubicrank_proto_rawDesc), len(file__3001_pubicrank_proto_rawDesc)))
	})
	return file__3001_pubicrank_proto_rawDescData
}

var file__3001_pubicrank_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file__3001_pubicrank_proto_goTypes = []any{
	(*PlayerAppearance)(nil), // 0: pb.PlayerAppearance
	(*RankEntryData)(nil),    // 1: pb.RankEntryData
	(*G2SUpdateRank)(nil),    // 2: pb.G2SUpdateRank
	(*C2SGetRankList)(nil),   // 3: pb.C2SGetRankList
	(*S2CGetRankList)(nil),   // 4: pb.S2CGetRankList
	(*C2SGetPlayerRank)(nil), // 5: pb.C2SGetPlayerRank
	(*S2CGetPlayerRank)(nil), // 6: pb.S2CGetPlayerRank
	(ERankType)(0),           // 7: pb.ERankType
	(ResponseCode)(0),        // 8: pb.response_code
}
var file__3001_pubicrank_proto_depIdxs = []int32{
	0, // 0: pb.RankEntryData.appearance:type_name -> pb.PlayerAppearance
	7, // 1: pb.G2SUpdateRank.rank_type:type_name -> pb.ERankType
	1, // 2: pb.G2SUpdateRank.entry:type_name -> pb.RankEntryData
	7, // 3: pb.C2SGetRankList.rank_type:type_name -> pb.ERankType
	8, // 4: pb.S2CGetRankList.code:type_name -> pb.response_code
	1, // 5: pb.S2CGetRankList.entries:type_name -> pb.RankEntryData
	7, // 6: pb.C2SGetPlayerRank.rank_type:type_name -> pb.ERankType
	8, // 7: pb.S2CGetPlayerRank.code:type_name -> pb.response_code
	1, // 8: pb.S2CGetPlayerRank.entry:type_name -> pb.RankEntryData
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file__3001_pubicrank_proto_init() }
func file__3001_pubicrank_proto_init() {
	if File__3001_pubicrank_proto != nil {
		return
	}
	file_code_proto_init()
	file_gameconfig_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file__3001_pubicrank_proto_rawDesc), len(file__3001_pubicrank_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file__3001_pubicrank_proto_goTypes,
		DependencyIndexes: file__3001_pubicrank_proto_depIdxs,
		MessageInfos:      file__3001_pubicrank_proto_msgTypes,
	}.Build()
	File__3001_pubicrank_proto = out.File
	file__3001_pubicrank_proto_goTypes = nil
	file__3001_pubicrank_proto_depIdxs = nil
}
