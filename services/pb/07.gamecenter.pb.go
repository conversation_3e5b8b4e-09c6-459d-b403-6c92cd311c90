// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: 07.gamecenter.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MiniGameInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	ExpireTime    *int32                 `protobuf:"varint,2,req,name=expireTime" json:"expireTime,omitempty"` // 有效期至,时间戳
	BuyTime       *int32                 `protobuf:"varint,3,req,name=buyTime" json:"buyTime,omitempty"`       // 购买时间
	GameTime      *int32                 `protobuf:"varint,4,req,name=gameTime" json:"gameTime,omitempty"`     // 游戏时间
	IsBuy         *bool                  `protobuf:"varint,5,req,name=isBuy" json:"isBuy,omitempty"`           // 是否购买
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiniGameInfo) Reset() {
	*x = MiniGameInfo{}
	mi := &file__07_gamecenter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiniGameInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiniGameInfo) ProtoMessage() {}

func (x *MiniGameInfo) ProtoReflect() protoreflect.Message {
	mi := &file__07_gamecenter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiniGameInfo.ProtoReflect.Descriptor instead.
func (*MiniGameInfo) Descriptor() ([]byte, []int) {
	return file__07_gamecenter_proto_rawDescGZIP(), []int{0}
}

func (x *MiniGameInfo) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *MiniGameInfo) GetExpireTime() int32 {
	if x != nil && x.ExpireTime != nil {
		return *x.ExpireTime
	}
	return 0
}

func (x *MiniGameInfo) GetBuyTime() int32 {
	if x != nil && x.BuyTime != nil {
		return *x.BuyTime
	}
	return 0
}

func (x *MiniGameInfo) GetGameTime() int32 {
	if x != nil && x.GameTime != nil {
		return *x.GameTime
	}
	return 0
}

func (x *MiniGameInfo) GetIsBuy() bool {
	if x != nil && x.IsBuy != nil {
		return *x.IsBuy
	}
	return false
}

// 游戏中心初始化
// 10300
// USED
type S2CGameCenterInit struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MiniGames     []*MiniGameInfo        `protobuf:"bytes,1,rep,name=miniGames" json:"miniGames,omitempty"` // 小游戏列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CGameCenterInit) Reset() {
	*x = S2CGameCenterInit{}
	mi := &file__07_gamecenter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CGameCenterInit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CGameCenterInit) ProtoMessage() {}

func (x *S2CGameCenterInit) ProtoReflect() protoreflect.Message {
	mi := &file__07_gamecenter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CGameCenterInit.ProtoReflect.Descriptor instead.
func (*S2CGameCenterInit) Descriptor() ([]byte, []int) {
	return file__07_gamecenter_proto_rawDescGZIP(), []int{1}
}

func (x *S2CGameCenterInit) GetMiniGames() []*MiniGameInfo {
	if x != nil {
		return x.MiniGames
	}
	return nil
}

// 游戏中心更新
// 10301
// USED
type S2CGameCenterUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MiniGame      *MiniGameInfo          `protobuf:"bytes,1,req,name=miniGame" json:"miniGame,omitempty"` // 更新的小游戏信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CGameCenterUpdate) Reset() {
	*x = S2CGameCenterUpdate{}
	mi := &file__07_gamecenter_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CGameCenterUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CGameCenterUpdate) ProtoMessage() {}

func (x *S2CGameCenterUpdate) ProtoReflect() protoreflect.Message {
	mi := &file__07_gamecenter_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CGameCenterUpdate.ProtoReflect.Descriptor instead.
func (*S2CGameCenterUpdate) Descriptor() ([]byte, []int) {
	return file__07_gamecenter_proto_rawDescGZIP(), []int{2}
}

func (x *S2CGameCenterUpdate) GetMiniGame() *MiniGameInfo {
	if x != nil {
		return x.MiniGame
	}
	return nil
}

// 购买小游戏
// 10302
// USED
type C2SGameCenterBuyMiniGame struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MiniGameId    *int32                 `protobuf:"varint,1,req,name=miniGameId" json:"miniGameId,omitempty"` // 小游戏ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SGameCenterBuyMiniGame) Reset() {
	*x = C2SGameCenterBuyMiniGame{}
	mi := &file__07_gamecenter_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SGameCenterBuyMiniGame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SGameCenterBuyMiniGame) ProtoMessage() {}

func (x *C2SGameCenterBuyMiniGame) ProtoReflect() protoreflect.Message {
	mi := &file__07_gamecenter_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SGameCenterBuyMiniGame.ProtoReflect.Descriptor instead.
func (*C2SGameCenterBuyMiniGame) Descriptor() ([]byte, []int) {
	return file__07_gamecenter_proto_rawDescGZIP(), []int{3}
}

func (x *C2SGameCenterBuyMiniGame) GetMiniGameId() int32 {
	if x != nil && x.MiniGameId != nil {
		return *x.MiniGameId
	}
	return 0
}

// 返回购买小游戏结果
// 10303
// USED
type S2CGameCenterBuyMiniGame struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,req,name=code,enum=pb.ResponseCode" json:"code,omitempty"` // 响应码
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`                 // 响应消息
	MiniGame      *MiniGameInfo          `protobuf:"bytes,3,opt,name=miniGame" json:"miniGame,omitempty"`               // 购买的小游戏信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CGameCenterBuyMiniGame) Reset() {
	*x = S2CGameCenterBuyMiniGame{}
	mi := &file__07_gamecenter_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CGameCenterBuyMiniGame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CGameCenterBuyMiniGame) ProtoMessage() {}

func (x *S2CGameCenterBuyMiniGame) ProtoReflect() protoreflect.Message {
	mi := &file__07_gamecenter_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CGameCenterBuyMiniGame.ProtoReflect.Descriptor instead.
func (*S2CGameCenterBuyMiniGame) Descriptor() ([]byte, []int) {
	return file__07_gamecenter_proto_rawDescGZIP(), []int{4}
}

func (x *S2CGameCenterBuyMiniGame) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CGameCenterBuyMiniGame) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CGameCenterBuyMiniGame) GetMiniGame() *MiniGameInfo {
	if x != nil {
		return x.MiniGame
	}
	return nil
}

// 开始小游戏
// 10304
// USED
type C2SGameCenterStartMiniGame struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MiniGameId    *int32                 `protobuf:"varint,1,req,name=miniGameId" json:"miniGameId,omitempty"` // 小游戏ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SGameCenterStartMiniGame) Reset() {
	*x = C2SGameCenterStartMiniGame{}
	mi := &file__07_gamecenter_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SGameCenterStartMiniGame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SGameCenterStartMiniGame) ProtoMessage() {}

func (x *C2SGameCenterStartMiniGame) ProtoReflect() protoreflect.Message {
	mi := &file__07_gamecenter_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SGameCenterStartMiniGame.ProtoReflect.Descriptor instead.
func (*C2SGameCenterStartMiniGame) Descriptor() ([]byte, []int) {
	return file__07_gamecenter_proto_rawDescGZIP(), []int{5}
}

func (x *C2SGameCenterStartMiniGame) GetMiniGameId() int32 {
	if x != nil && x.MiniGameId != nil {
		return *x.MiniGameId
	}
	return 0
}

// 结束小游戏
// 10305
// USED
type C2SGameCenterEndMiniGame struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MiniGameId    *int32                 `protobuf:"varint,1,req,name=miniGameId" json:"miniGameId,omitempty"` // 小游戏ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SGameCenterEndMiniGame) Reset() {
	*x = C2SGameCenterEndMiniGame{}
	mi := &file__07_gamecenter_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SGameCenterEndMiniGame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SGameCenterEndMiniGame) ProtoMessage() {}

func (x *C2SGameCenterEndMiniGame) ProtoReflect() protoreflect.Message {
	mi := &file__07_gamecenter_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SGameCenterEndMiniGame.ProtoReflect.Descriptor instead.
func (*C2SGameCenterEndMiniGame) Descriptor() ([]byte, []int) {
	return file__07_gamecenter_proto_rawDescGZIP(), []int{6}
}

func (x *C2SGameCenterEndMiniGame) GetMiniGameId() int32 {
	if x != nil && x.MiniGameId != nil {
		return *x.MiniGameId
	}
	return 0
}

var File__07_gamecenter_proto protoreflect.FileDescriptor

const file__07_gamecenter_proto_rawDesc = "" +
	"\n" +
	"\x1307.gamecenter.proto\x12\x02pb\x1a\n" +
	"code.proto\x1a\x10gameconfig.proto\"\x8a\x01\n" +
	"\fMiniGameInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x02(\x05R\x02id\x12\x1e\n" +
	"\n" +
	"expireTime\x18\x02 \x02(\x05R\n" +
	"expireTime\x12\x18\n" +
	"\abuyTime\x18\x03 \x02(\x05R\abuyTime\x12\x1a\n" +
	"\bgameTime\x18\x04 \x02(\x05R\bgameTime\x12\x14\n" +
	"\x05isBuy\x18\x05 \x02(\bR\x05isBuy\"C\n" +
	"\x11S2CGameCenterInit\x12.\n" +
	"\tminiGames\x18\x01 \x03(\v2\x10.pb.MiniGameInfoR\tminiGames\"C\n" +
	"\x13S2CGameCenterUpdate\x12,\n" +
	"\bminiGame\x18\x01 \x02(\v2\x10.pb.MiniGameInfoR\bminiGame\":\n" +
	"\x18C2SGameCenterBuyMiniGame\x12\x1e\n" +
	"\n" +
	"miniGameId\x18\x01 \x02(\x05R\n" +
	"miniGameId\"\x89\x01\n" +
	"\x18S2CGameCenterBuyMiniGame\x12%\n" +
	"\x04code\x18\x01 \x02(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12,\n" +
	"\bminiGame\x18\x03 \x01(\v2\x10.pb.MiniGameInfoR\bminiGame\"<\n" +
	"\x1aC2SGameCenterStartMiniGame\x12\x1e\n" +
	"\n" +
	"miniGameId\x18\x01 \x02(\x05R\n" +
	"miniGameId\":\n" +
	"\x18C2SGameCenterEndMiniGame\x12\x1e\n" +
	"\n" +
	"miniGameId\x18\x01 \x02(\x05R\n" +
	"miniGameIdB&Z$kairo_paradise_server/services/pb;pb"

var (
	file__07_gamecenter_proto_rawDescOnce sync.Once
	file__07_gamecenter_proto_rawDescData []byte
)

func file__07_gamecenter_proto_rawDescGZIP() []byte {
	file__07_gamecenter_proto_rawDescOnce.Do(func() {
		file__07_gamecenter_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file__07_gamecenter_proto_rawDesc), len(file__07_gamecenter_proto_rawDesc)))
	})
	return file__07_gamecenter_proto_rawDescData
}

var file__07_gamecenter_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file__07_gamecenter_proto_goTypes = []any{
	(*MiniGameInfo)(nil),               // 0: pb.MiniGameInfo
	(*S2CGameCenterInit)(nil),          // 1: pb.S2CGameCenterInit
	(*S2CGameCenterUpdate)(nil),        // 2: pb.S2CGameCenterUpdate
	(*C2SGameCenterBuyMiniGame)(nil),   // 3: pb.C2SGameCenterBuyMiniGame
	(*S2CGameCenterBuyMiniGame)(nil),   // 4: pb.S2CGameCenterBuyMiniGame
	(*C2SGameCenterStartMiniGame)(nil), // 5: pb.C2SGameCenterStartMiniGame
	(*C2SGameCenterEndMiniGame)(nil),   // 6: pb.C2SGameCenterEndMiniGame
	(ResponseCode)(0),                  // 7: pb.response_code
}
var file__07_gamecenter_proto_depIdxs = []int32{
	0, // 0: pb.S2CGameCenterInit.miniGames:type_name -> pb.MiniGameInfo
	0, // 1: pb.S2CGameCenterUpdate.miniGame:type_name -> pb.MiniGameInfo
	7, // 2: pb.S2CGameCenterBuyMiniGame.code:type_name -> pb.response_code
	0, // 3: pb.S2CGameCenterBuyMiniGame.miniGame:type_name -> pb.MiniGameInfo
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file__07_gamecenter_proto_init() }
func file__07_gamecenter_proto_init() {
	if File__07_gamecenter_proto != nil {
		return
	}
	file_code_proto_init()
	file_gameconfig_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file__07_gamecenter_proto_rawDesc), len(file__07_gamecenter_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file__07_gamecenter_proto_goTypes,
		DependencyIndexes: file__07_gamecenter_proto_depIdxs,
		MessageInfos:      file__07_gamecenter_proto_msgTypes,
	}.Build()
	File__07_gamecenter_proto = out.File
	file__07_gamecenter_proto_goTypes = nil
	file__07_gamecenter_proto_depIdxs = nil
}
