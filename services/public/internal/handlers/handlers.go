package handlers

import (
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/public/internal/handlers/rank"
)

// Register registers all message handlers for the public service
func Register() {

	// Register other handlers as needed
	rank.Register()

	logger.Info("Public service handlers registered")
}

// RegisterMessageHandlers is the legacy function, now it calls Register
func RegisterMessageHandlers() {
	Register()
}
