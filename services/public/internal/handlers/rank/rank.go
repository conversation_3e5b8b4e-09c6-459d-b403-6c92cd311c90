package rank

import (
	"context"
	"github.com/golang/protobuf/proto"
	"kairo_paradise_server/internal/handler"
	"kairo_paradise_server/services/pb"
	"kairo_paradise_server/services/pb/msg"
	"kairo_paradise_server/services/public/internal/module/rank"
)

func updateRank(ctx context.Context, payload []byte) (proto.Message, error) {
	resp := &pb.S2GResponse{
		Code: pb.ResponseCode_fail.Enum(),
	}
	in := &pb.G2SUpdateRank{}
	err := proto.Unmarshal(payload, in)
	if err != nil {
		return resp, err
	}
	if err := rank.GetRankManager().UpdateRankEntry(ctx, int32(*in.RankType), rank.NewEntryFromProto(in.Entry)); err != nil {
		return resp, err
	}
	return &pb.S2GResponse{
		Code: pb.ResponseCode_normal.Enum(),
	}, nil
}

func Register() {
	handler.Register(msg.PCK_G2SUpdateRank, updateRank)
}
