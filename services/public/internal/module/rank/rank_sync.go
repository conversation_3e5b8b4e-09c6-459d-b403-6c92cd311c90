package rank

import (
	"context"
	"fmt"
	"kairo_paradise_server/internal/logger"
	"sync"
	"time"

	"go.uber.org/zap"
)

// SyncManager 数据同步管理器
type SyncManager struct {
	configManager *ConfigManager
	memoryStore   *MemoryStore

	// 同步控制
	syncInterval time.Duration // 同步间隔
	ticker       *time.Ticker  // 定时器
	stopCh       chan struct{} // 停止信号
	wg           sync.WaitGroup
	mutex        sync.RWMutex

	// 同步状态
	lastSyncTime map[int32]int64 // 每个排行榜的最后同步时间
	syncRunning  bool            // 是否正在同步
}

// NewSyncManager 创建同步管理器
func NewSyncManager(configManager *ConfigManager, memoryStore *MemoryStore) *SyncManager {
	return &SyncManager{
		configManager: configManager,
		memoryStore:   memoryStore,
		syncInterval:  5 * time.Minute, // 固定5分钟同步一次
		stopCh:        make(chan struct{}),
		lastSyncTime:  make(map[int32]int64),
	}
}

// Start 启动同步管理器
func (sm *SyncManager) Start() error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	if sm.syncRunning {
		return fmt.Errorf("sync manager already running")
	}
	logger.Info("Starting rank sync manager")
	if err := sm.loadFromDatabase(); err != nil {
		logger.Error("Failed to load data from database", zap.Error(err))
		// 不返回错误，允许服务继续启动
	}
	// 启动定时同步
	sm.ticker = time.NewTicker(sm.syncInterval)
	sm.syncRunning = true

	sm.wg.Add(1)
	go sm.syncLoop()

	logger.Info("Rank sync manager started successfully")
	return nil
}

// Stop 停止同步管理器
func (sm *SyncManager) Stop() error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	if !sm.syncRunning {
		return nil
	}

	logger.Info("Stopping rank sync manager")

	// 停止定时器
	if sm.ticker != nil {
		sm.ticker.Stop()
	}
	// 发送停止信号
	close(sm.stopCh)
	// 等待同步循环结束
	sm.wg.Wait()
	// 最后一次同步到数据库
	if err := sm.syncToDatabase(); err != nil {
		logger.Error("Failed to sync data to database during shutdown", zap.Error(err))
	}
	if err := sm.createShutdownSnapshots(context.Background()); err != nil {
		logger.Error("Failed to create shutdown snapshots", zap.Error(err))
	}
	// 创建快照
	sm.syncRunning = false
	logger.Info("Rank sync manager stopped")
	return nil
}

// syncLoop 同步循环
func (sm *SyncManager) syncLoop() {
	defer sm.wg.Done()

	for {
		select {
		case <-sm.ticker.C:
			if err := sm.syncToDatabase(); err != nil {
				logger.Error("Failed to sync data to database", zap.Error(err))
			}
		case <-sm.stopCh:
			return
		}
	}
}

// loadFromDatabase 从数据库加载数据到内存
func (sm *SyncManager) loadFromDatabase() error {
	configs := sm.configManager.GetAllConfigs()
	for rankType, _ := range configs {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)

		entries, err := LoadLatestSnapshot(ctx, rankType)
		if err != nil {
			cancel()
			logger.Error("Failed to load rank data from database", zap.Int32("rankType", rankType), zap.Error(err))
			continue
		}
		// 将数据加载到内存存储
		if len(entries) > 0 {
			if err := sm.memoryStore.BatchUpdate(ctx, rankType, entries); err != nil {
				cancel()
				logger.Error("Failed to load data to memory store", zap.Int32("rankType", rankType), zap.Error(err))
				continue
			}
			logger.Info("Loaded rank data from database", zap.Int32("rankType", rankType), zap.Int("entryCount", len(entries)))
		}
		cancel()
	}
	logger.Info("Finished loading rank data from database")
	return nil
}

// syncToDatabase 同步数据到数据库
func (sm *SyncManager) syncToDatabase() error {
	configs := sm.configManager.GetAllConfigs()
	for rankType, config := range configs {
		// 检查是否需要同步
		if !sm.shouldSync(rankType) {
			continue
		}
		ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
		// 从内存获取排行榜数据
		entries, _, err := sm.memoryStore.GetRankList(rankType, 1, config.MaxRankLimit)
		if err != nil {
			cancel()
			logger.Error("Failed to get rank data from memory", zap.Int32("rankType", rankType), zap.Error(err))
			continue
		}
		// 保存到数据库
		if len(entries) > 0 {
			if err := saveRankData(ctx, rankType, entries); err != nil {
				cancel()
				logger.Error("Failed to save rank data to database", zap.Int32("rankType", rankType), zap.Error(err))
				continue
			}
		}
		// 更新最后同步时间
		sm.mutex.Lock()
		sm.lastSyncTime[rankType] = time.Now().Unix()
		sm.mutex.Unlock()
		cancel()
	}
	logger.Debug("Finished syncing rank data to database")
	return nil
}

// shouldSync 判断是否需要同步
func (sm *SyncManager) shouldSync(rankType int32) bool {
	sm.mutex.RLock()
	lastSync, exists := sm.lastSyncTime[rankType]
	sm.mutex.RUnlock()
	if !exists {
		return true // 首次同步
	}
	// 固定5分钟同步一次
	syncInterval := int64(300) // 5分钟 = 300秒
	return time.Now().Unix()-lastSync >= syncInterval
}

// GetSyncStatus 获取同步状态
func (sm *SyncManager) GetSyncStatus() map[int32]int64 {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	result := make(map[int32]int64)
	for rankType, lastSync := range sm.lastSyncTime {
		result[rankType] = lastSync
	}
	return result
}

func (sm *SyncManager) createShutdownSnapshots(ctx context.Context) error {
	configs := sm.configManager.GetAllConfigs()
	for rankType, config := range configs {
		// 获取排行榜数据
		entries, _, err := sm.memoryStore.GetRankList(rankType, 1, config.MaxRankLimit)
		if err != nil {
			logger.Error("Failed to get rank data from memory", zap.Int32("rankType", rankType), zap.Error(err))
			continue
		}
		// 创建停服快照
		if len(entries) > 0 {
			if err := CreateShutdownSnapshot(ctx, rankType, entries); err != nil {
				logger.Error("Failed to create shutdown snapshot", zap.Int32("rankType", rankType), zap.Error(err))
			}
		}
	}
	return nil
}
