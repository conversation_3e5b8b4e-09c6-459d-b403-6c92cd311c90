package rank

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/logger"
	"sort"
	"sync"
	"time"
)

// MemoryStore 内存排行榜存储
type MemoryStore struct {
	// 排行榜数据，按排行榜类型分组
	rankData map[int32]*MemoryRankData
	// 读写锁，每个排行榜一个锁
	rankMutex map[int32]*sync.RWMutex
	// 玩家信息管理器
	playerInfoManager *PlayerInfoManager
	// 是否已初始化
	initialized bool
}

// NewMemoryStore 创建新的内存存储
func NewMemoryStore() *MemoryStore {
	return &MemoryStore{
		rankData:          make(map[int32]*MemoryRankData),
		rankMutex:         make(map[int32]*sync.RWMutex),
		playerInfoManager: NewPlayerInfoManager(),
		initialized:       false,
	}
}

// InitializeRankTypes 初始化所有排行榜类型
func (ms *MemoryStore) InitializeRankTypes(rankTypes []int32) {
	if ms.initialized {
		logger.Warn("MemoryStore already initialized")
		return
	}

	// 提前创建所有排行榜的数据和锁
	for _, rankType := range rankTypes {
		ms.rankData[rankType] = NewMemoryRankData()
		ms.rankMutex[rankType] = &sync.RWMutex{}
	}

	ms.initialized = true
}

// getRankMutex 获取指定排行榜的锁
func (ms *MemoryStore) getRankMutex(rankType int32) *sync.RWMutex {
	// 直接从map中获取，因为已经预先初始化了所有排行榜类型
	mutex, exists := ms.rankMutex[rankType]
	if !exists {
		logger.Error("Rank type not initialized", zap.Int32("rankType", rankType))
		// 但这种情况不应该出现，为了避免panic创建一个新锁
		ms.rankMutex[rankType] = &sync.RWMutex{}
		return ms.rankMutex[rankType]
	}
	return mutex
}

// getRankData 获取指定排行榜的数据
func (ms *MemoryStore) getRankData(rankType int32) *MemoryRankData {
	// 直接从map中获取，因为已经预先初始化了所有排行榜类型
	data, exists := ms.rankData[rankType]
	if !exists {
		logger.Error("Rank type not initialized", zap.Int32("rankType", rankType))
		// 但这种情况不应该出现，为了避免panic创建一个新数据
		ms.rankData[rankType] = NewMemoryRankData()
		return ms.rankData[rankType]
	}
	return data
}

// UpdateEntry 更新排行榜条目
func (ms *MemoryStore) UpdateEntry(rankType int32, entry *Entry, config *Config) (int32, int32, error) {
	if entry == nil || entry.PlayerInfo == nil || entry.RankEntry == nil {
		return -1, -1, fmt.Errorf("invalid entry data")
	}

	// 先更新玩家基本外观信息，这个没想好是不是每次都需要更新，还是上榜之后才更新
	ms.playerInfoManager.UpdatePlayerInfo(entry.PlayerInfo)

	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.Lock()
	defer mutex.Unlock()

	playerID := entry.PlayerInfo.PlayerID
	score := entry.RankEntry.Score

	oldRank := int32(-1)
	if _, exists := data.PlayerMap[playerID]; exists {
		oldRank = ms.findPlayerRank(data, playerID)
	}

	// 检查分数是否达到动态最低分数限制
	if score < config.DynamicMinScoreLimit {
		// 如果玩家之前在榜上，需要移除，保持列表长度
		if _, exists := data.PlayerMap[playerID]; exists {
			ms.removeRankEntry(data, playerID)
			// 移除玩家后需要重新构建排序列表和更新动态限制
			ms.rebuildSortedList(data, config)
			return -1, oldRank, nil
		}
		return -1, -1, nil
	}

	rankEntry := &RankEntry{
		PlayerID:   playerID,
		Score:      score,
		UpdateTime: time.Now().Unix(),
	}
	// 使用二分插入排序进行增量更新
	if oldRank != -1 {
		// 更新已存在的玩家
		ms.updateExistingPlayer(data, rankEntry, oldRank, config)
	} else {
		// 插入新玩家
		ms.insertNewPlayer(data, rankEntry, config)
	}

	// 查找新排名
	newRank := ms.findPlayerRank(data, playerID)

	return newRank, oldRank, nil
}

// BatchUpdate 批量更新排行榜条目（用于从数据库加载数据）
func (ms *MemoryStore) BatchUpdate(ctx context.Context, rankType int32, entries []*Entry) error {
	if len(entries) == 0 {
		return nil
	}
	_ = ctx // 暂时不使用context参数
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)
	mutex.Lock()
	defer mutex.Unlock()

	// 清空现有数据
	data.Entries = make([]*RankEntry, 0, len(entries))
	data.PlayerMap = make(map[uint64]*RankEntry)

	// 批量添加条目
	for _, entry := range entries {
		if entry.RankEntry == nil {
			continue
		}
		rankEntry := &RankEntry{
			PlayerID:   entry.RankEntry.PlayerID,
			Score:      entry.RankEntry.Score,
			UpdateTime: entry.RankEntry.UpdateTime,
			Ranking:    entry.RankEntry.Ranking,
		}
		// 手动将排行榜上的玩家数据加载到内存中
		ms.playerInfoManager.UpdatePlayerInfo(entry.PlayerInfo)

		data.Entries = append(data.Entries, rankEntry)
		data.PlayerMap[rankEntry.PlayerID] = rankEntry
	}

	// 重新排序
	ms.scoreSort(data.Entries)

	data.TotalCount = int64(len(data.Entries))
	data.UpdateTime = time.Now().Unix()

	logger.Debug("Batch update completed",
		zap.Int32("rankType", rankType),
		zap.Int("finalCount", len(data.Entries)))

	return nil
}

// removeRankEntry 移除排行榜条目
func (ms *MemoryStore) removeRankEntry(data *MemoryRankData, playerID uint64) {
	delete(data.PlayerMap, playerID)

	// 从排序列表中移除
	for i, entry := range data.Entries {
		if entry.PlayerID == playerID {
			data.Entries = append(data.Entries[:i], data.Entries[i+1:]...)
			break
		}
	}

	data.TotalCount = int64(len(data.Entries))
	data.UpdateTime = time.Now().Unix()
}

// rebuildSortedList 重新构建排序列表（优化版本）
func (ms *MemoryStore) rebuildSortedList(data *MemoryRankData, config *Config) {
	playerMapLen := len(data.PlayerMap)

	// 如果没有数据，直接清空
	if playerMapLen == 0 {
		data.Entries = data.Entries[:0] // 重用切片，避免重新分配
		data.TotalCount = 0
		data.UpdateTime = time.Now().Unix()
		return
	}

	// 重用现有切片或创建新切片
	if cap(data.Entries) < playerMapLen {
		data.Entries = make([]*RankEntry, 0, playerMapLen)
	} else {
		data.Entries = data.Entries[:0] // 重用现有切片
	}

	// 将所有条目放入切片
	for _, entry := range data.PlayerMap {
		data.Entries = append(data.Entries, entry)
	}

	// 全量排序
	ms.scoreSort(data.Entries)

	// 限制排行榜大小
	ms.enforceRankLimit(data, config)

	// 批量更新排名，这个可以去掉，因为返回给客户端的列表是有序的
	//ms.batchUpdateRankings(data.Entries)

	// 动态更新MinScoreLimit
	ms.updateDynamicMinScoreLimit(config, data.Entries)

	data.TotalCount = int64(len(data.Entries))
	data.UpdateTime = time.Now().Unix()
}

// scoreSort 排序算法
func (ms *MemoryStore) scoreSort(entries []*RankEntry) {
	sort.Slice(entries, func(i, j int) bool {
		if entries[i].Score == entries[j].Score {
			// 分数相同时，按更新时间升序排序（先更新的排名靠前）
			if entries[i].UpdateTime == entries[j].UpdateTime {
				return entries[i].PlayerID < entries[j].PlayerID
			}
			return entries[i].UpdateTime < entries[j].UpdateTime
		}
		return entries[i].Score > entries[j].Score
	})
}

// batchUpdateRankings 批量更新排名
func (ms *MemoryStore) batchUpdateRankings(entries []*RankEntry) {
	for i, entry := range entries {
		entry.Ranking = int32(i + 1)
	}
}

// updateExistingPlayer 更新已存在的玩家
func (ms *MemoryStore) updateExistingPlayer(data *MemoryRankData, newEntry *RankEntry, oldRank int32, config *Config) {
	playerID := newEntry.PlayerID

	// 更新PlayerMap中的条目
	data.PlayerMap[playerID] = newEntry

	// 找到玩家在Entries中的当前位置
	oldIndex := oldRank - 1
	if oldIndex < 0 || oldIndex >= int32(len(data.Entries)) {
		// 如果位置无效，回退到完全重建
		ms.rebuildSortedList(data, config)
		return
	}
	// 更新位置
	idx := ms.updatePlayerInPlace(data, newEntry, oldIndex)

	// 对于已存在榜上的玩家，也是需要做动态分数更新，如果出现退位到最后一名的情况，就需要更新处理
	if idx >= config.MaxRankLimit-1 {
		ms.updateDynamicMinScoreLimit(config, data.Entries)
	}

	data.TotalCount = int64(len(data.Entries))
	data.UpdateTime = time.Now().Unix()
}

// insertNewPlayer 插入新玩家
func (ms *MemoryStore) insertNewPlayer(data *MemoryRankData, newEntry *RankEntry, config *Config) {
	// 更新PlayerMap中的条目
	data.PlayerMap[newEntry.PlayerID] = newEntry

	// 使用二分查找找到插入位置
	insertPos := ms.binarySearchInsertPosition(data.Entries, newEntry)

	// 插入新玩家
	ms.insertPlayerAtPosition(data, newEntry, insertPos)

	// 检查排行榜大小限制
	originalSize := int32(len(data.Entries))
	ms.enforceRankLimit(data, config)
	sizeChanged := int32(len(data.Entries)) != originalSize

	// 只更新从插入位置开始的排名，避免全量排行处理
	ms.updateRankingsFromPosition(data, insertPos)

	if sizeChanged || int32(len(data.Entries)) >= config.MaxRankLimit {
		ms.updateDynamicMinScoreLimit(config, data.Entries)
	}

	data.TotalCount = int64(len(data.Entries))
	data.UpdateTime = time.Now().Unix()
}

// insertPlayerAtPosition 在指定位置插入玩家
func (ms *MemoryStore) insertPlayerAtPosition(data *MemoryRankData, entry *RankEntry, position int32) {
	// 扩展切片
	data.Entries = append(data.Entries, nil)

	copy(data.Entries[position+1:], data.Entries[position:len(data.Entries)-1])

	// 插入新条目
	data.Entries[position] = entry
}

// updateRankingsFromPosition 从指定位置开始更新排名
func (ms *MemoryStore) updateRankingsFromPosition(data *MemoryRankData, startPos int32) {
	// 从插入位置开始更新所有后续排名
	for i := startPos; i < int32(len(data.Entries)); i++ {
		data.Entries[i].Ranking = i + 1
	}
}

// updatePlayerInPlace 更新玩家位置
func (ms *MemoryStore) updatePlayerInPlace(data *MemoryRankData, newEntry *RankEntry, currentIndex int32) int32 {
	// 保存旧条目信息
	oldEntry := data.Entries[currentIndex]

	// 快速检查：如果分数没有变化，只需要更新时间和排名
	if oldEntry.Score == newEntry.Score {
		// 分数相同，只需要更新条目数据，位置不变
		data.Entries[currentIndex] = newEntry
		newEntry.Ranking = currentIndex + 1
		return currentIndex
	}

	// 分数发生变化，需要重新定位
	// 先临时移除当前条目，以便进行二分查找
	ms.removePlayerFromEntries(data, currentIndex)
	// 使用二分查找找到正确的插入位置
	newPos := ms.binarySearchInsertPosition(data.Entries, newEntry)

	// 在新位置插入条目
	ms.insertPlayerAtPosition(data, newEntry, newPos)

	// 更新受影响范围的排名
	ms.updateRankingsInRange(data, currentIndex, newPos)
	return newPos
}

// updateRankingsInRange 更新指定范围内的排名（优化版本）
func (ms *MemoryStore) updateRankingsInRange(data *MemoryRankData, oldIndex, newIndex int32) {
	// 确定需要更新的范围
	start := oldIndex
	end := newIndex

	if start > end {
		start, end = end, start
	}

	// 确保范围在有效区间内
	if start < 0 {
		start = 0
	}
	if end >= int32(len(data.Entries)) {
		end = int32(len(data.Entries)) - 1
	}

	// 扩展更新范围以确保正确性
	// 因为插入/删除操作可能影响更大范围的排名
	if start > 0 {
		start--
	}
	if end < int32(len(data.Entries))-1 {
		end++
	}

	// 更新受影响范围的排名
	for i := start; i <= end && i < int32(len(data.Entries)); i++ {
		data.Entries[i].Ranking = i + 1
	}
}

// binarySearchInsertPosition 二分查找插入位置
func (ms *MemoryStore) binarySearchInsertPosition(entries []*RankEntry, newEntry *RankEntry) int32 {
	left, right := int32(0), int32(len(entries))

	for left < right {
		mid := left + (right-left)/2

		if ms.shouldInsertBefore(newEntry, entries[mid]) {
			right = mid
		} else {
			left = mid + 1
		}
	}

	return left
}

// shouldInsertBefore 判断新条目是否应该插入到目标条目之前
func (ms *MemoryStore) shouldInsertBefore(newEntry, targetEntry *RankEntry) bool {
	// 按分数降序排序
	if newEntry.Score != targetEntry.Score {
		return newEntry.Score > targetEntry.Score
	}

	// 分数相同时，按更新时间升序排序（先更新的排名靠前）
	if newEntry.UpdateTime != targetEntry.UpdateTime {
		return newEntry.UpdateTime < targetEntry.UpdateTime
	}

	// 时间也相同时，按玩家ID升序排序
	return newEntry.PlayerID < targetEntry.PlayerID
}

// removePlayerFromEntries 从Entries中移除指定位置的玩家
func (ms *MemoryStore) removePlayerFromEntries(data *MemoryRankData, index int32) {
	if index < 0 || index >= int32(len(data.Entries)) {
		return
	}
	// 移除条目
	copy(data.Entries[index:], data.Entries[index+1:])
	data.Entries = data.Entries[:len(data.Entries)-1]
}

// enforceRankLimit 强制执行排行榜大小限制
func (ms *MemoryStore) enforceRankLimit(data *MemoryRankData, config *Config) {
	if int32(len(data.Entries)) > config.MaxRankLimit {
		// 移除超出限制的条目
		for i := config.MaxRankLimit; i < int32(len(data.Entries)); i++ {
			delete(data.PlayerMap, data.Entries[i].PlayerID)
		}
		data.Entries = data.Entries[:config.MaxRankLimit]
	}
}

// updateAllRankings 更新所有条目的排名
func (ms *MemoryStore) updateAllRankings(data *MemoryRankData) {
	for i, entry := range data.Entries {
		entry.Ranking = int32(i + 1)
	}
	data.TotalCount = int64(len(data.Entries))
}

// updateDynamicMinScoreLimit 动态更新最低分数限制
func (ms *MemoryStore) updateDynamicMinScoreLimit(config *Config, entries []*RankEntry) {
	// 如果排行榜已满员，将MinScoreLimit设置为最后一名的分数
	if int32(len(entries)) >= config.MaxRankLimit && len(entries) > 0 {
		lastEntry := entries[len(entries)-1]
		newMinLimit := lastEntry.Score

		// 动态限制应该始终等于最后一名的分数（当排行榜满员时）
		// 这样可以确保：
		// 1. 分数高于最后一名的玩家可以上榜
		// 2. 分数低于最后一名的玩家无法上榜
		// 3. 当榜上玩家分数降低时，动态限制也会相应降低
		if newMinLimit != config.DynamicMinScoreLimit {
			config.UpdateDynamicMinScoreLimit(newMinLimit)
		}
	} else if int32(len(entries)) < config.MaxRankLimit {
		// 如果排行榜未满员，动态限制应该回到静态限制
		// 这样可以让更多玩家有机会上榜
		if config.DynamicMinScoreLimit != config.MinScoreLimit {
			config.UpdateDynamicMinScoreLimit(config.MinScoreLimit)
		}
	}
}

// findPlayerRank 查找玩家排名
func (ms *MemoryStore) findPlayerRank(data *MemoryRankData, playerID uint64) int32 {
	// 首先检查玩家是否在排行榜中
	rankEntry, exists := data.PlayerMap[playerID]
	if !exists {
		return -1
	}
	// 直接返回已存储的排名
	return rankEntry.Ranking
}

// GetRankList 获取排行榜列表
func (ms *MemoryStore) GetRankList(rankType int32, start, count int32) ([]*Entry, int64, error) {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.RLock()
	defer mutex.RUnlock()

	if start < 1 {
		start = 1
	}

	startIndex := start - 1
	endIndex := startIndex + count

	if startIndex >= int32(len(data.Entries)) {
		return []*Entry{}, data.TotalCount, nil
	}

	if endIndex > int32(len(data.Entries)) {
		endIndex = int32(len(data.Entries))
	}

	// 获取排行榜条目对应的玩家ID列表
	playerIDs := make([]uint64, endIndex-startIndex)
	for i := startIndex; i < endIndex; i++ {
		playerIDs[i-startIndex] = data.Entries[i].PlayerID
	}

	// 批量获取玩家信息
	playerInfoMap := ms.playerInfoManager.GetPlayerInfoBatch(playerIDs)

	// 组合完整的Entry
	result := make([]*Entry, endIndex-startIndex)
	for i := startIndex; i < endIndex; i++ {
		rankEntry := data.Entries[i]
		playerInfo := playerInfoMap[rankEntry.PlayerID]

		// 如果玩家信息不存在，创建一个默认的
		if playerInfo == nil {
			playerInfo = &PlayerInfo{
				PlayerID:   rankEntry.PlayerID,
				PlayerName: fmt.Sprintf("Player%d", rankEntry.PlayerID),
				Level:      1,
				Icon:       1,
				UpdateTime: rankEntry.UpdateTime,
			}
		}

		// 复制数据以避免外部修改
		result[i-startIndex] = &Entry{
			PlayerInfo: &PlayerInfo{
				PlayerID:   playerInfo.PlayerID,
				PlayerName: playerInfo.PlayerName,
				Level:      playerInfo.Level,
				Icon:       playerInfo.Icon,
				UpdateTime: playerInfo.UpdateTime,
			},
			RankEntry: &RankEntry{
				PlayerID:   rankEntry.PlayerID,
				Score:      rankEntry.Score,
				UpdateTime: rankEntry.UpdateTime,
				Ranking:    rankEntry.Ranking,
			},
		}
	}

	return result, data.TotalCount, nil
}

// GetPlayerRank 获取玩家排名
func (ms *MemoryStore) GetPlayerRank(rankType int32, playerID uint64) (int32, *Entry, error) {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.RLock()
	defer mutex.RUnlock()

	rankEntry, exists := data.PlayerMap[playerID]
	if !exists {
		return -1, nil, nil
	}

	rank := ms.findPlayerRank(data, playerID)

	// 获取玩家基础信息
	playerInfo := ms.playerInfoManager.GetPlayerInfo(playerID)
	if playerInfo == nil {
		// 如果玩家信息不存在，创建一个默认的
		playerInfo = &PlayerInfo{
			PlayerID:   playerID,
			PlayerName: fmt.Sprintf("Player%d", playerID),
			Level:      1,
			Icon:       1,
			UpdateTime: rankEntry.UpdateTime,
		}
	}

	// 组合完整的Entry
	entry := &Entry{
		PlayerInfo: &PlayerInfo{
			PlayerID:   playerInfo.PlayerID,
			PlayerName: playerInfo.PlayerName,
			Level:      playerInfo.Level,
			Icon:       playerInfo.Icon,
			UpdateTime: playerInfo.UpdateTime,
		},
		RankEntry: &RankEntry{
			PlayerID:   rankEntry.PlayerID,
			Score:      rankEntry.Score,
			UpdateTime: rankEntry.UpdateTime,
			Ranking:    rankEntry.Ranking,
		},
	}

	return rank, entry, nil
}

// GetStats 获取统计信息
func (ms *MemoryStore) GetStats(rankType int32) (int64, int64) {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.RLock()
	defer mutex.RUnlock()

	return data.TotalCount, data.UpdateTime
}

// ClearRank 清空排行榜
func (ms *MemoryStore) ClearRank(rankType int32) error {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.Lock()
	defer mutex.Unlock()

	data.Entries = make([]*RankEntry, 0)
	data.PlayerMap = make(map[uint64]*RankEntry)
	data.TotalCount = 0
	data.UpdateTime = time.Now().Unix()

	logger.Info("Rank cleared", zap.Int32("rankType", rankType))
	return nil
}
