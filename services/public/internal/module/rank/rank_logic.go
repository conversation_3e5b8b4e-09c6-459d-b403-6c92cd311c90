package rank

import (
	"context"
	"errors"
	"fmt"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/services/pb"
	"time"
)

func saveRankData(ctx context.Context, rankType int32, entries []*Entry) error {
	if len(entries) == 0 {
		return nil
	}
	logger.Debug("Saving rank data to database", zap.Int32("rankType", rankType), zap.Int("entryCount", len(entries)))
	tx := bootstrap.MysqlDb.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 先删除该排行榜类型的旧数据
	if err := tx.Where("rank_type = ?", rankType).Delete(&models.Rank{}).Error; err != nil {
		tx.Rollback()
		return err
	}
	batchSize := 100
	for i := 0; i < len(entries); i += batchSize {
		end := i + batchSize
		if end > len(entries) {
			end = len(entries)
		}

		batch := entries[i:end]
		rankDataList := make([]*models.Rank, 0, len(batch))

		for rank, entry := range batch {
			if entry.PlayerInfo == nil || entry.RankEntry == nil {
				continue
			}

			// 准备排行榜数据
			rankData := &models.Rank{
				RankType:    rankType,
				Ranking:     int32(i + rank + 1), // 排名从1开始
				PlayerID:    entry.PlayerInfo.PlayerID,
				Score:       entry.RankEntry.Score,
				UpdatedAt:   entry.RankEntry.UpdateTime,
				UpdatedTime: time.Now(),
			}
			rankDataList = append(rankDataList, rankData)
		}
		if len(rankDataList) > 0 {
			if err := tx.Create(&rankDataList).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	return nil
}

func LoadRankData(ctx context.Context, rankType int32, limit int32) ([]*Entry, error) {
	var rankDataList []models.Rank
	query := bootstrap.MysqlDb.WithContext(ctx).
		Where("rank_type = ?", rankType).
		Order("ranking ASC")

	if limit > 0 {
		query = query.Limit(int(limit))
	}

	if err := query.Find(&rankDataList).Error; err != nil {
		return nil, fmt.Errorf("failed to load rank data: %w", err)
	}

	entries := make([]*Entry, 0, len(rankDataList))
	for _, data := range rankDataList {
		entry := &Entry{
			RankEntry: &RankEntry{
				PlayerID:   data.PlayerID,
				Score:      data.Score,
				UpdateTime: data.UpdatedAt,
				Ranking:    data.Ranking,
			},
		}
		entries = append(entries, entry)
	}
	return entries, nil
}

// GetRankCount 获取排行榜总数量
func GetRankCount(ctx context.Context, rankType int32) (int64, error) {
	var count int64
	if err := bootstrap.MysqlDb.WithContext(ctx).Model(&models.Rank{}).Where("rank_type = ?", rankType).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to get rank count: %w", err)
	}
	return count, nil
}

// DeleteRankData 删除指定排行榜的数据
func DeleteRankData(ctx context.Context, rankType int32) error {
	logger.Debug("Deleting rank data from database", zap.Int32("rankType", rankType))

	if err := bootstrap.MysqlDb.WithContext(ctx).Where("rank_type = ?", rankType).Delete(&models.Rank{}).Error; err != nil {
		return fmt.Errorf("failed to delete rank data: %w", err)
	}

	logger.Debug("Rank data deleted from database", zap.Int32("rankType", rankType))
	return nil
}

// CreateShutdownSnapshot 创建停服快照
func CreateShutdownSnapshot(ctx context.Context, rankType int32, entries []*Entry) error {
	if len(entries) == 0 {
		return nil
	}
	protoEntries := make([]*pb.RankEntryData, 0, len(entries))
	for _, entry := range entries {
		if entry.RankEntry == nil || entry.PlayerInfo == nil {
			continue
		}
		protoEntry := entry.ToProto()
		if protoEntry != nil {
			protoEntries = append(protoEntries, protoEntry)
		}
	}
	rankList := &pb.S2CGetRankList{
		Entries:    protoEntries,
		TotalCount: proto.Int32(int32(len(protoEntries))),
	}

	// 序列化为二进制
	snapshotData, err := proto.Marshal(rankList)
	if err != nil {
		return fmt.Errorf("failed to marshal proto data: %w", err)
	}

	// 创建快照记录
	snapshot := &models.RankSnapshot{
		RankType:     rankType,
		SnapshotTime: time.Now().Unix(),
		TotalCount:   int64(len(protoEntries)),
		DataVersion:  2, // 版本2表示使用proto格式
		Snapshot:     snapshotData,
	}
	if err := bootstrap.MysqlDb.WithContext(ctx).Create(snapshot).Error; err != nil {
		return fmt.Errorf("failed to create shutdown snapshot: %w", err)
	}
	return nil
}

func LoadLatestSnapshot(ctx context.Context, rankType int32) ([]*Entry, error) {
	var snapshot models.RankSnapshot
	err := bootstrap.MysqlDb.WithContext(ctx).Where("rank_type = ?", rankType).Order("snapshot_time DESC").First(&snapshot).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to load snapshot: %w", err)
	}

	// 反序列化proto数据
	return parseProtoSnapshot(snapshot.Snapshot)
}

func parseProtoSnapshot(snapshotData []byte) ([]*Entry, error) {
	if len(snapshotData) == 0 {
		return nil, nil
	}

	// 反序列化proto数据
	rankList := &pb.S2CGetRankList{}
	if err := proto.Unmarshal(snapshotData, rankList); err != nil {
		return nil, fmt.Errorf("failed to unmarshal proto data: %w", err)
	}

	// 转换为Entry格式
	entries := make([]*Entry, 0, len(rankList.Entries))
	for _, protoEntry := range rankList.Entries {
		entry := &Entry{
			PlayerInfo: &PlayerInfo{
				PlayerID:   protoEntry.GetPlayerId(),
				PlayerName: protoEntry.GetPlayerName(),
				Level:      protoEntry.GetLevel(),
				Icon:       protoEntry.GetIcon(),
				Prosperity: protoEntry.GetProsperity(),
				UpdateTime: time.Now().Unix(), // 快照加载时使用当前时间
			},
			RankEntry: &RankEntry{
				PlayerID:   protoEntry.GetPlayerId(),
				Score:      protoEntry.GetScore(),
				UpdateTime: time.Now().Unix(),
				Ranking:    protoEntry.GetRanking(),
			},
		}
		entries = append(entries, entry)
	}
	return entries, nil
}
