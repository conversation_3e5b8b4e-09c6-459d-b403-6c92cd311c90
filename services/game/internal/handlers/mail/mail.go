package mail

import (
	"context"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/handler"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/game/internal/module/player/mail"
	"kairo_paradise_server/services/game/internal/module/player_manage"
	"kairo_paradise_server/services/game/internal/utils"
	"kairo_paradise_server/services/pb"
	"kairo_paradise_server/services/pb/msg"
)

// HandleMailList handles the request to get mail list
func HandleMailList(ctx context.Context, payload []byte) (proto.Message, error) {
	uid := utils.GetUIDFromContext(ctx)
	playerInfo, ok := player_manage.Manager.GetPlayerByUid(uid)
	if !ok {
		return &pb.S2CCommResponse{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String("player not found"),
		}, nil
	}

	list := mail.GetMailListApi(playerInfo)
	return &pb.S2CMailList{
		Mails: list,
	}, nil
}

// HandleMailRead handles the request to read a mail
func HandleMailRead(ctx context.Context, payload []byte) (proto.Message, error) {
	request := &pb.C2SMailRead{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal C2SMailRead", zap.Error(err))
		return nil, err
	}

	uid := utils.GetUIDFromContext(ctx)
	playerInfo, ok := player_manage.Manager.GetPlayerByUid(uid)
	if !ok {
		return &pb.S2CCommResponse{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String("player not found"),
		}, nil
	}

	_, err = mail.ReadMailApi(playerInfo, *request.MailId)
	if err != nil {
		return &pb.S2CCommResponse{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String(err.Error()),
		}, nil
	}

	return &pb.S2CCommResponse{
		Code: pb.ResponseCode_normal.Enum(),
	}, nil
}

// HandleMailClaim handles the request to claim mail attachments
func HandleMailClaim(ctx context.Context, payload []byte) (proto.Message, error) {
	request := &pb.C2SMailClaim{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal C2SMailClaim", zap.Error(err))
		return nil, err
	}

	uid := utils.GetUIDFromContext(ctx)
	playerInfo, ok := player_manage.Manager.GetPlayerByUid(uid)
	if !ok {
		return &pb.S2CCommResponse{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String("player not found"),
		}, nil
	}

	// Claim mail attachments
	err = mail.ClaimMailAttachmentsApi(playerInfo, *request.MailId)
	if err != nil {
		return &pb.S2CCommResponse{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String(err.Error()),
		}, nil
	}

	return &pb.S2CCommResponse{
		Code: pb.ResponseCode_normal.Enum(),
	}, nil
}

// HandleMailDelete handles the request to delete a mail
func HandleMailDelete(ctx context.Context, payload []byte) (proto.Message, error) {
	request := &pb.C2SMailDelete{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal C2SMailDelete", zap.Error(err))
		return nil, err
	}

	uid := utils.GetUIDFromContext(ctx)
	playerInfo, ok := player_manage.Manager.GetPlayerByUid(uid)
	if !ok {
		return &pb.S2CCommResponse{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String("player not found"),
		}, nil
	}

	err = mail.DeleteMailApi(playerInfo, *request.MailId)
	if err != nil {
		return &pb.S2CCommResponse{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String(err.Error()),
		}, nil
	}
	return &pb.S2CCommResponse{
		Code: pb.ResponseCode_normal.Enum(),
	}, nil
}

// HandleGMSendMail handles the request to send a mail from GM
func HandleGMSendMail(ctx context.Context, payload []byte) (proto.Message, error) {
	request := &pb.C2SGMSendMail{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal C2SGMSendMail", zap.Error(err))
		return nil, err
	}

	uid := utils.GetUIDFromContext(ctx)
	// Send mail to specific player
	playerInfo, ok := player_manage.Manager.GetPlayerByUid(uid)
	if !ok {
		return &pb.S2CCommResponse{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String("player not found"),
		}, nil
	}

	_, err = mail.SendCustomMailApi(
		playerInfo,
		*request.Title,
		*request.Content,
		*request.Sender,
		request.Attachments,
		*request.ExpireSeconds,
	)
	if err != nil {
		return &pb.S2CCommResponse{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String(err.Error()),
		}, nil
	}
	return &pb.S2CCommResponse{
		Code: pb.ResponseCode_normal.Enum(),
	}, nil
}

// Register registers all mail handlers
func Register() {
	handler.Register(msg.PCK_C2SMailList, HandleMailList)
	handler.Register(msg.PCK_C2SMailRead, HandleMailRead)
	handler.Register(msg.PCK_C2SMailClaim, HandleMailClaim)
	handler.Register(msg.PCK_C2SMailDelete, HandleMailDelete)
	handler.Register(msg.PCK_C2SGMSendMail, HandleGMSendMail)
}
