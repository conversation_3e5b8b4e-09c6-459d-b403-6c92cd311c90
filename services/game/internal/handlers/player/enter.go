package player

import (
	"context"
	"errors"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/handler"
	"kairo_paradise_server/internal/logger"
	player2 "kairo_paradise_server/services/game/internal/logic/player"
	"kairo_paradise_server/services/game/internal/module/player"
	"kairo_paradise_server/services/game/internal/module/player_manage"
	"kairo_paradise_server/services/game/internal/utils"
	"kairo_paradise_server/services/pb"
	"kairo_paradise_server/services/pb/msg"
)

// EnterGame 处理进入游戏请求
// 因为在gate服连接websocket时，已经初始化过gRPC连接，创建了player信息，所以这里不需要初始化player
func EnterGame(ctx context.Context, in *pb.C2SEnterGame) (*pb.S2CEnterGame, error) {
	resp := &pb.S2CEnterGame{
		Code:       pb.ResponseCode_fail.Enum(),
		IsRegister: proto.Bool(false),
		PlayerId:   proto.Uint64(0),
	}
	uid := utils.GetUIDFromContext(ctx)
	playerInfo, ok := player_manage.Manager.GetPlayerByUid(uid)
	if !ok {
		return resp, errors.New("player not found")
	}
	if playerInfo.GetPlayerId() > 0 {
		playerInfo.SetIsReconnect(true)
		// 已经登录过，直接返回
		resp.Code = pb.ResponseCode_normal.Enum()
		resp.IsRegister = proto.Bool(playerInfo.IsRegister())
		resp.PlayerId = proto.Uint64(playerInfo.GetPlayerId())
		// 登录回调
		playerInfo.OnLoginSuccess()
		return resp, nil
	}

	info, err := player2.GetPlayerInfo(uid, consts.ServerId)
	if err != nil {
		return resp, errors.New("get player info error: " + err.Error())
	}

	// 加载设置玩家信息
	player.LoadPlayerInfo(playerInfo, info)

	// TODO 这里需要判断用户账号状况，能不能进游戏

	playerInfo.OnAfterLoad()
	playerInfo.OnLoginSuccess()
	logger.Debugf("player info: %+v", playerInfo)

	return &pb.S2CEnterGame{
		Code:       pb.ResponseCode_normal.Enum(),
		IsRegister: proto.Bool(playerInfo.IsRegister()),
		PlayerId:   proto.Uint64(info.Id),
	}, nil
}

// HandleEnterGame 处理进入游戏请求
func HandleEnterGame(ctx context.Context, payload []byte) (proto.Message, error) {
	// 解析请求消息
	request := &pb.C2SEnterGame{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal C2SEnterGame", zap.Error(err))
		return nil, err
	}
	return EnterGame(ctx, request)
}

// HandleDisconnect 处理断开连接请求
func HandleDisconnect(ctx context.Context, payload []byte) (proto.Message, error) {
	uid := utils.GetUIDFromContext(ctx)
	playerInfo, ok := player_manage.Manager.GetPlayerByUid(uid)
	if !ok {
		return &pb.S2GEmpty{}, errors.New("player not found")
	}
	playerInfo.OnDisconnect()
	return &pb.S2GEmpty{}, nil
}

// Register 注册消息处理函数
func Register() {
	handler.Register(msg.PCK_C2SEnterGame, HandleEnterGame)
	handler.Register(msg.PCK_C2SPlayerModifyInfo, HandlePlayerModifyInfo)
	handler.Register(msg.PCK_G2SDisconnect, HandleDisconnect)
}
