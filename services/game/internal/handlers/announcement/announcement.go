package announcement

import (
	"context"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/handler"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/game/internal/module/player/announcement"
	"kairo_paradise_server/services/game/internal/module/player_manage"
	"kairo_paradise_server/services/game/internal/utils"
	"kairo_paradise_server/services/pb"
	"kairo_paradise_server/services/pb/msg"
)

// HandleAnnouncementRead handles the request to read an announcement
func HandleAnnouncementRead(ctx context.Context, payload []byte) (proto.Message, error) {
	request := &pb.C2SAnnouncementRead{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal C2SAnnouncementRead", zap.Error(err))
		return nil, err
	}

	uid := utils.GetUIDFromContext(ctx)
	playerInfo, ok := player_manage.Manager.GetPlayerByUid(uid)
	if !ok {
		return &pb.S2CAnnouncementRead{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String("player not found"),
		}, nil
	}

	announcementInfo, err := announcement.ReadAnnouncementApi(playerInfo, *request.AnnouncementId)
	if err != nil {
		return &pb.S2CAnnouncementRead{
			Code:    pb.ResponseCode_fail.Enum(),
			Message: proto.String(err.Error()),
		}, nil
	}

	return &pb.S2CAnnouncementRead{
		Code:         pb.ResponseCode_normal.Enum(),
		Announcement: announcementInfo,
	}, nil
}

// Register registers all announcement handlers
func Register() {
	handler.Register(msg.PCK_C2SAnnouncementRead, HandleAnnouncementRead)
}
