package scene

import (
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/services/pb"
)

// UpdateSceneApi 更新玩家场景数据的API接口
// 参数:
//   - p: 玩家接口
//   - buildList: 新的建筑列表
//
// 功能:
//   - 更新玩家场景数据
//   - 计算并更新繁荣度
//   - 处理物品变更（添加/移除建筑时的物品增减）
//   - 保存场景数据到数据库
func UpdateSceneApi(p I.IPlayer, buildList []*pb.BuildData) {
	UpdateScene(p, buildList)
}

// GetSceneDataApi 获取玩家场景数据的API接口
// 参数:
//   - p: 玩家接口
//
// 返回:
//   - 场景数据的protobuf消息
func GetSceneDataApi(p I.IPlayer) *pb.S2CBuildDataInit {
	sceneData := p.GetScene()
	return &pb.S2CBuildDataInit{
		Builds: sceneData.BuildList,
	}
}

// ClearSceneApi 清空玩家场景的API接口
// 参数:
//   - p: 玩家接口
//
// 功能:
//   - 清空场景中的所有建筑
//   - 返还所有建筑对应的物品到背包
//   - 更新繁荣度
//   - 保存场景数据
func ClearSceneApi(p I.IPlayer) {
	scene := p.GetScene()

	// 返还所有建筑对应的物品
	for _, build := range scene.BuildList {
		returnItemForBuild(p, build)
	}

	// 清空建筑列表
	UpdateScene(p, []*pb.BuildData{})
}

// SaveSceneApi 保存玩家场景数据的API接口
// 参数:
//   - p: 玩家接口
//
// 功能:
//   - 保存场景数据到数据库
func SaveSceneApi(p I.IPlayer) {
	SaveScene(p.GetScene())
}
