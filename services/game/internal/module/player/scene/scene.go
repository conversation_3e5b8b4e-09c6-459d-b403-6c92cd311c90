package scene

import (
	"errors"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/services/game/internal/module/player/assets"
	"kairo_paradise_server/services/pb"
)

func LoadPlayerScene(p I.IPlayer) {
	playerId := p.GetPlayerId()
	info := &models.PlayerAsset{}
	err := bootstrap.MysqlDb.Model(&models.PlayerAsset{}).Where("player_id = ?", playerId).Select("scene").Take(info).Error
	if err != nil && !errors.As(err, &gorm.ErrRecordNotFound) {
		return
	}
	scene := &models.Scene{}
	scene.PlayerId = playerId
	if info.PlayerId == 0 {
		info.PlayerId = playerId
		bootstrap.MysqlDb.Create(info)
	} else {
		var l pb.ListBuildData
		_ = proto.Unmarshal(info.Scene, &l)
		scene.BuildList = l.Builds
	}
	p.SetScene(scene)
}

func SaveScene(scene *models.Scene) {
	pr := &pb.ListBuildData{
		Builds: scene.BuildList,
	}
	b, err := proto.Marshal(pr)
	if err != nil {
		logger.Error("marshal scene error", zap.Error(err))
		return
	}
	playerAssets := models.PlayerAsset{Scene: b}
	if err := bootstrap.MysqlDb.Model(&models.PlayerAsset{}).Where("player_id = ?", scene.PlayerId).Updates(&playerAssets).Error; err != nil {
		logger.Error("save scene error", zap.Error(err))
		return
	}
}

func UpdateScene(p I.IPlayer, buildList []*pb.BuildData) {
	scene := p.GetScene()

	// 计算分数 - 根据建筑物数量和类型计算繁荣度
	prosperity := calculateProsperity(buildList)

	// 更新玩家繁荣度
	playerInfo := p.GetPlayerInfo()
	if playerInfo != nil && playerInfo.Prosperity != prosperity {
		playerInfo.Prosperity = prosperity
		// 保存繁荣度到数据库
		// TODO 这里实际上还需要根据积分计算世界等级
		updates := models.Player{
			Prosperity: prosperity,
		}
		bootstrap.MysqlDb.Model(&models.Player{}).Where("id = ?", playerInfo.Id).Updates(updates)
	}

	// 处理物品变更 - 比较新旧建筑列表，返还移除的建筑对应的物品
	processItemChanges(p, scene.BuildList, buildList)

	// 更新场景数据
	scene.BuildList = buildList

	// TODO 先实时保存，后面改成定时保存
	SaveScene(scene)
}

// calculateProsperity 根据建筑列表计算繁荣度
func calculateProsperity(buildList []*pb.BuildData) int32 {
	// todo 暂时未确定计算算法
	baseProsperity := int32(len(buildList) * 10)
	return baseProsperity
}

// processItemChanges 处理建筑变更导致的物品变化
func processItemChanges(p I.IPlayer, oldBuildList, newBuildList []*pb.BuildData) {
	// 创建映射以便快速查找
	newBuildMap := make(map[int32]*pb.BuildData)
	oldBuildMap := make(map[int32]*pb.BuildData)

	// 构建新建筑映射
	for _, build := range newBuildList {
		newBuildMap[*build.Id] = build
	}

	// 构建旧建筑映射
	for _, build := range oldBuildList {
		oldBuildMap[*build.Id] = build
	}

	// 1. 查找在旧列表中存在但在新列表中不存在的建筑（被移除的建筑）
	for id, oldBuild := range oldBuildMap {
		if _, exists := newBuildMap[id]; !exists {
			// 建筑被移除，返还对应的物品到背包
			returnItemForBuild(p, oldBuild)
		}
	}

	// 2. 查找在新列表中存在但在旧列表中不存在的建筑（新增的建筑）
	for id, newBuild := range newBuildMap {
		if _, exists := oldBuildMap[id]; !exists {
			// 新增建筑，从背包扣除对应的物品
			deductItemForBuild(p, newBuild)
		}
	}
}

// returnItemForBuild 返还建筑对应的物品到玩家背包
func returnItemForBuild(p I.IPlayer, build *pb.BuildData) {
	itemId := *build.ItemId

	count := int32(1)
	addType := pb.EResourceAddType_EResourceAddType_None
	assets.AddItemApi(p, itemId, count, &addType)
	logger.Info("Building removed, returning item to player", zap.Uint64("player_id", p.GetPlayerId()), zap.Int32("item_id", itemId), zap.Int32("count", count))
}

// deductItemForBuild 从玩家背包中扣除建筑所需的物品
func deductItemForBuild(p I.IPlayer, build *pb.BuildData) {
	itemId := *build.ItemId
	count := int32(1)
	subType := pb.EResourceSubType_EResourceSubType_None
	assets.SubItemApi(p, itemId, count, &subType)
	logger.Info("Building added, deducting item from player", zap.Uint64("player_id", p.GetPlayerId()), zap.Int32("item_id", itemId), zap.Int32("count", count))
}
