package player

import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/discovery"
	"kairo_paradise_server/internal/grpc"
	"kairo_paradise_server/internal/models"
)

type MsgInfo struct {
	msgId int32
	pck   interface{}
}

type EvtInfo struct {
	evtId int32
	pck   interface{}
}

type Player struct {
	UserId      uint32 // 账号ID
	Id          uint64 // 玩家角色ID
	isRobot     bool   // 是否机器人
	isReconnect bool   // 是否重连

	State          consts.PlayerState
	StateStartTime int64

	serviceMaps map[discovery.ServiceType]string       // 玩家所分配的服务信息
	services    map[discovery.ServiceType]*grpc.Client // 玩家所分配的gRPC客户端

	Info     *models.Player   // 玩家信息
	UserInfo *models.UserInfo // 登录账号信息

	Assets          *models.Assets                // 资产信息
	Scene           *models.Scene                 // 场景信息
	MailBox         *models.PlayerMailBox         // 邮件信息
	AnnouncementBox *models.PlayerAnnouncementBox // 公告信息

	recvMsg   []*MsgInfo // 收到的消息
	recvEvent []*EvtInfo // 收到的事件

	send chan []byte // 发送消息队列
	done chan struct{}

	// 清理任务的取消函数，用于取消之前的清理任务
	cleanupCancel func()
}
