package assets

import (
	"errors"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/game_config"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/services/pb"
	"time"
)

func LoadPlayerAssets(p I.IPlayer) {
	playerId := p.GetPlayerId()
	info := &models.PlayerAsset{}
	err := bootstrap.MysqlDb.Model(&models.PlayerAsset{}).Where("player_id = ?", playerId).Select("coin, item_list").Take(info).Error
	if err != nil && !errors.As(err, &gorm.ErrRecordNotFound) {
		return
	}
	assets := &models.Assets{}
	assets.PlayerId = playerId
	if info.PlayerId == 0 {
		info.PlayerId = playerId
		bootstrap.MysqlDb.Create(info)
		m := make(map[int32][]*pb.BagItem)
		assets.ItemList = m
		assets.SetMaxId(assets.CalculateMaxId())
		initItem(assets)
	} else {
		assets.Coin = info.Coin
		assets.ItemList = make(map[int32][]*pb.BagItem)
		var l pb.ListBagItem

		_ = proto.Unmarshal(info.ItemList, &l)
		for _, item := range l.Items {
			if item.Id != nil {
				id := *item.ItemId
				assets.ItemList[id] = append(assets.ItemList[id], item)
			}
		}
		assets.SetMaxId(info.MaxItemId)
	}
	p.SetAssets(assets)
}

// SaveAssets 保存玩家资产到数据库
func SaveAssets(assets *models.Assets) {
	if assets == nil {
		return
	}

	playerAssets := &models.PlayerAsset{
		Coin: assets.Coin,
	}

	pr := &pb.ListBagItem{
		Items: make([]*pb.BagItem, 0),
	}
	for _, v := range assets.ItemList {
		pr.Items = append(pr.Items, v...)
	}
	b, err := proto.Marshal(pr)
	if err != nil {
		logger.Error("marshal item list error", zap.Error(err))
		return
	}
	playerAssets.ItemList = b
	playerAssets.MaxItemId = assets.GetMaxId()

	if err := bootstrap.MysqlDb.Model(&models.PlayerAsset{}).Where("player_id = ?", assets.PlayerId).Updates(playerAssets).Error; err != nil {
		logger.Error("save assets error", zap.Error(err))
		return
	}
}

func GetItemList(assets *models.Assets) []*pb.BagItem {
	p := make([]*pb.BagItem, 0)
	for _, v := range assets.ItemList {
		p = append(p, v...)
	}
	return p
}

func AddCoin(assets *models.Assets, coin int32) {
	assets.Coin += coin
}

func SubCoin(assets *models.Assets, coin int32) {
	if assets.Coin <= coin {
		assets.Coin = 0
		return
	}
	assets.Coin -= coin
}

func CoinIsEnough(assets *models.Assets, dec int32) bool {
	return assets.Coin >= dec
}

// AddItem 向资产中添加指定数量的物品，处理叠加和过期逻辑
// 参数:
//
//	assets: 玩家资产对象指针
//	itemId: 要添加的物品ID
//	count:  需要添加的数量（必须大于0）
//
// 功能说明:
//  1. 自动处理物品过期时间
//  2. 支持物品叠加逻辑（根据配置的Overlay值）
//  3. 自动创建新物品槽位
//  4. Overlay为0则代表无限叠加
func addItem(assets *models.Assets, itemId int32, count int32) []*pb.BagItem {
	if count <= 0 {
		return nil
	}
	// {state:{NoUnkeyedLiterals:{} DoNotCompare:[] DoNotCopy:[] atomicMessageInfo:<nil>} Id:0xc000343d4c Name:0xc00028d510 Type:EItem_Money Params:[] Icon:0xc00028d520 Desc:0xc00028d530 ItemTab:<nil> Expire:<nil> Overlay:<nil> unknownFields:[] sizeCache:0}
	itemConfig := game_config.ItemConfig.Item(itemId)
	if itemConfig == nil || *itemConfig.Id == 0 {
		return nil
	}
	if itemConfig.Overlay == nil {
		itemConfig.Overlay = proto.Int32(999)
	}
	if itemConfig.ValidHours == nil {
		itemConfig.ValidHours = proto.Int32(0)
	}
	// 计算物品过期时间（基于当前时间和配置的过期小时数）
	now := time.Now().Unix()
	var expireTime int64
	if *itemConfig.ValidHours == 0 {
		expireTime = 0
	} else {
		expireTime = now + int64(*itemConfig.ValidHours)*3600
	}

	var addList []*pb.BagItem

	// 处理已有物品的叠加逻辑
	// 1. 检查相同过期时间的物品
	// 2. 判断是否达到最大叠加数量
	// 3. 自动拆分到多个槽位
	if items, ok := assets.ItemList[itemId]; ok && *itemConfig.Overlay != 1 {
		for _, item := range items {
			// 同过期时间才能放到同一个槽位中，便于处理过期
			if expireTime != *item.ExpireTime {
				continue
			}
			// 如果叠加数量已满，则跳过
			if *itemConfig.Overlay != 0 && *item.Count >= *itemConfig.Overlay {
				continue
			}
			// 放入格子中，但需要考虑如果不能一次性放满，则需要开新格子
			currentCount := *item.Count
			*item.Count += count
			if *itemConfig.Overlay > 0 && *item.Count > *itemConfig.Overlay {
				*item.Count = *itemConfig.Overlay
				// 计算剩余数量，继续处理
				count -= *itemConfig.Overlay - currentCount
				addList = append(addList, newBagItem(assets, itemConfig, *item.Id, count, now, expireTime))
				if count <= 0 {
					return addList
				}
			} else {
				// 物品完全放入当前槽位，无需继续处理
				addList = append(addList, newBagItem(assets, itemConfig, *item.Id, count, now, expireTime))
				return addList
			}
		}
	}

	// 处理不可叠加物品的特殊情况
	// 每个物品单独创建一个新的槽位
	if *itemConfig.Overlay == 1 {
		list := make([]*pb.BagItem, count)
		for i := 0; i < int(count); i++ {
			newItem := newBagItem(assets, itemConfig, 0, 1, now, expireTime)
			list = append(list, newItem)
			addList = append(addList, newBagItem(assets, itemConfig, *newItem.Id, 1, now, expireTime))
		}
		assets.ItemList[itemId] = append(assets.ItemList[itemId], list...)
		return append(addList, list...)
	}
	if *itemConfig.Overlay == 0 {
		newItem := newBagItem(assets, itemConfig, 0, count, now, expireTime)
		assets.ItemList[itemId] = append(assets.ItemList[itemId], newItem)
		addList = append(addList, newBagItem(assets, itemConfig, *newItem.Id, count, now, expireTime))
		return addList
	}

	// 计算需要创建的物品槽位数量
	j := (count + *itemConfig.Overlay - 1) / *itemConfig.Overlay
	for i := int32(0); i < j; i++ {
		currentCount := count
		if currentCount > *itemConfig.Overlay {
			currentCount = *itemConfig.Overlay
		}
		newItem := newBagItem(assets, itemConfig, 0, currentCount, now, expireTime)
		addList = append(addList, newItem)
		assets.ItemList[itemId] = append(assets.ItemList[itemId], newItem)
		count -= currentCount
	}
	return addList
}

func newBagItem(assets *models.Assets, itemConfig *pb.ListItemConfig_ItemConfig, id int32, count int32, now int64, expireTime int64) *pb.BagItem {
	if *itemConfig.Overlay > 0 && count > *itemConfig.Overlay {
		count = *itemConfig.Overlay
	}
	if id == 0 {
		id = assets.IncrMaxId()
	}

	newItem := &pb.BagItem{
		Id:         proto.Int32(id),
		ItemId:     proto.Int32(*itemConfig.Id),
		Count:      proto.Int32(count),
		CreateTime: proto.Int64(now),
		ExpireTime: proto.Int64(expireTime),
		Type:       itemConfig.Type,
	}

	return newItem
}

func subItem(assets *models.Assets, itemId int32, count int32) []*pb.BagItem {
	if count <= 0 {
		return nil
	}
	// Find the item
	items, exists := assets.ItemList[itemId]
	if !exists || len(items) == 0 {
		return nil // Item doesn't exist
	}

	leftNum := count
	var updateItems []*pb.BagItem
	var changeItems []*pb.BagItem // 存储所有被修改的物品信息

	// 遍历物品列表，优先扣减即将过期的物品
	// 按创建时间排序，优先消耗最早获得的物品
	for _, item := range items {
		if leftNum <= 0 {
			updateItems = append(updateItems, item)
			continue
		}
		typ := *item.Type

		// 如果当前物品数量足够扣减
		if *item.Count > leftNum {
			*item.Count -= leftNum
			leftNum = 0
			updateItems = append(updateItems, item)
			changeItems = append(changeItems, &pb.BagItem{
				Id:         proto.Int32(*item.Id),
				Count:      &leftNum,
				ItemId:     proto.Int32(*item.ItemId),
				CreateTime: proto.Int64(*item.CreateTime),
				ExpireTime: proto.Int64(*item.ExpireTime),
				Type:       &typ,
			})
		} else {
			// 当前物品数量不足，全部扣减并继续
			leftNum -= *item.Count
			changeItems = append(changeItems, &pb.BagItem{
				Id:         proto.Int32(*item.Id),
				Count:      proto.Int32(*item.Count),
				ItemId:     proto.Int32(*item.ItemId),
				CreateTime: proto.Int64(*item.CreateTime),
				ExpireTime: proto.Int64(*item.ExpireTime),
				Type:       &typ,
			})
			// 不添加到更新列表中，相当于删除此物品
		}
	}

	// 如果还有剩余未扣减的数量，表示物品不足
	if leftNum > 0 {
		return updateItems
	}
	// 更新物品列表
	if len(updateItems) > 0 {
		assets.ItemList[itemId] = updateItems
	} else {
		// 如果没有剩余物品，删除该物品ID的条目
		delete(assets.ItemList, itemId)
	}

	return changeItems
}

// useItem 使用物品的核心逻辑，根据物品类型执行不同的操作
// 参数:
//
//	assets: 玩家资产对象指针
//	itemId: 物品配置ID
//	id: 物品实例ID
//	count: 使用数量
//
// 返回值:
//
//	[]*pb.BagItem: 被消耗的物品列表
func useItem(assets *models.Assets, itemId int32, id int32, count int32) []*pb.BagItem {
	if count <= 0 {
		return nil
	}
	// 获取物品配置信息
	itemConfig := game_config.ItemConfig.Item(itemId)
	if itemConfig == nil || *itemConfig.Id == 0 {
		return nil
	}
	// 查找物品实例
	items, exists := assets.ItemList[itemId]
	if !exists || len(items) == 0 {
		return nil
	}

	// 查找指定ID的物品及其在列表中的索引
	var targetItem *pb.BagItem
	var targetIndex int = -1
	for i, item := range items {
		if *item.Id == id {
			targetItem = item
			targetIndex = i
			break
		}
	}

	if targetItem == nil || targetIndex == -1 {
		return nil
	}

	if *targetItem.Count < count {
		return nil
	}

	// 创建一份物品的副本，用于返回给客户端
	itemType := *targetItem.Type
	changeItem := &pb.BagItem{
		Id:         proto.Int32(*targetItem.Id),
		Count:      proto.Int32(count),
		ItemId:     proto.Int32(*targetItem.ItemId),
		CreateTime: proto.Int64(*targetItem.CreateTime),
		ExpireTime: proto.Int64(*targetItem.ExpireTime),
		Type:       &itemType,
	}

	switch itemType {
	case pb.EItem_EItem_Box:
		// 宝箱类物品，可能需要生成奖励物品
		// TODO 需要调用生成随机奖励的逻辑
		// 消耗物品
		return consumeItemFromSlot(assets, itemId, targetIndex, count)
	case pb.EItem_EItem_HeadIcon:
		// 头像类物品，使用后应该激活头像，但不消耗物品
		// 暂时只返回物品信息，不实际消耗
		return []*pb.BagItem{changeItem}
	case pb.EItem_EItem_Title:
		// 称号类物品，使用后应该激活称号，但不消耗物品
		// 暂时只返回物品信息，不实际消耗
		return []*pb.BagItem{changeItem}
	case pb.EItem_EItem_Coupon:
		// 消耗物品
		return consumeItemFromSlot(assets, itemId, targetIndex, count)

	case pb.EItem_EItem_Money:
		return consumeItemFromSlot(assets, itemId, targetIndex, count)
	default:
		// 默认情况下，消耗物品
		return consumeItemFromSlot(assets, itemId, targetIndex, count)
	}
}

// consumeItemFromSlot 从指定的物品槽位中消耗物品
// 参数:
//
//	assets: 玩家资产对象指针
//	itemId: 物品配置ID
//	slotIndex: 物品槽位索引
//	count: 消耗数量
//
// 返回值:
//
//	[]*pb.BagItem: 被消耗的物品列表
func consumeItemFromSlot(assets *models.Assets, itemId int32, slotIndex int, count int32) []*pb.BagItem {
	items, exists := assets.ItemList[itemId]
	if !exists || slotIndex < 0 || slotIndex >= len(items) {
		return nil
	}

	targetItem := items[slotIndex]
	if *targetItem.Count < count {
		return nil // 数量不足
	}

	// 创建一份物品的副本，用于返回给客户端
	itemType := *targetItem.Type
	changeItem := &pb.BagItem{
		Id:         proto.Int32(*targetItem.Id),
		Count:      proto.Int32(count),
		ItemId:     proto.Int32(*targetItem.ItemId),
		CreateTime: proto.Int64(*targetItem.CreateTime),
		ExpireTime: proto.Int64(*targetItem.ExpireTime),
		Type:       &itemType,
	}

	// 更新物品数量
	*targetItem.Count -= count

	// 如果物品数量变为0，则从列表中移除
	if *targetItem.Count <= 0 {
		// 移除该槽位
		items = append(items[:slotIndex], items[slotIndex+1:]...)

		// 如果该物品类型没有剩余槽位，则删除整个条目
		if len(items) == 0 {
			delete(assets.ItemList, itemId)
		} else {
			assets.ItemList[itemId] = items
		}
	}

	return []*pb.BagItem{changeItem}
}

// 初始化玩家物品
func initItem(assets *models.Assets) {
	itemConfig := game_config.ConstConfig.Item(1701)
	if itemConfig == nil || itemConfig.VIntLList == nil {
		return
	}
	for _, item := range itemConfig.VIntLList {
		if item.A == nil || len(item.A) < 2 {
			continue
		}
		addItem(assets, item.A[0], item.A[1])
	}
}
