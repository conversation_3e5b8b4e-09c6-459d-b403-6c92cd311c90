package announcement

import (
	"errors"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/services/pb"
	"time"
)

// LoadPlayerAnnouncements loads player announcements from database
func LoadPlayerAnnouncements(p I.IPlayer) {
	playerId := p.GetPlayerId()

	announcementBox := models.NewPlayerAnnouncementBox(playerId)

	var dbAnnouncements []models.PlayerAnnouncement
	err := bootstrap.MysqlDb.Model(&models.PlayerAnnouncement{}).
		Where("player_id = ? AND deleted_at = 0", playerId).
		Find(&dbAnnouncements).Error

	if err != nil && !errors.As(err, &gorm.ErrRecordNotFound) {
		logger.Error("Failed to load player announcements", zap.Error(err), zap.Uint64("player_id", playerId))
		p.SetAnnouncementBox(announcementBox)
		return
	}

	// Convert database announcements to memory announcements
	for _, dbAnnouncement := range dbAnnouncements {
		if dbAnnouncement.EndTime > 0 && dbAnnouncement.EndTime < time.Now().Unix() {
			continue
		}

		announcement := &models.Announcement{
			Id:             dbAnnouncement.Id,
			PlayerId:       dbAnnouncement.PlayerId,
			AnnouncementId: dbAnnouncement.AnnouncementId,
			Type:           dbAnnouncement.Type,
			Title:          dbAnnouncement.Title,
			Content:        dbAnnouncement.Content,
			Status:         dbAnnouncement.Status,
			Priority:       dbAnnouncement.Priority,
			CreatedAt:      dbAnnouncement.CreatedAt,
			StartTime:      dbAnnouncement.StartTime,
			EndTime:        dbAnnouncement.EndTime,
			ShowInterval:   dbAnnouncement.ShowInterval,
			JumpTarget:     dbAnnouncement.JumpTarget,
		}

		announcementBox.Announcements[announcement.Id] = announcement
		if announcement.Id > announcementBox.GetMaxId() {
			announcementBox.SetMaxId(announcement.Id)
		}
	}

	// Load global announcements that the player hasn't seen yet
	var globalAnnouncements []models.GlobalAnnouncement
	err = bootstrap.MysqlDb.Model(&models.GlobalAnnouncement{}).
		Where("deleted_at = 0 AND end_time > ?", time.Now().Unix()).
		Find(&globalAnnouncements).Error

	if err != nil && !errors.As(err, &gorm.ErrRecordNotFound) {
		logger.Error("Failed to load global announcements", zap.Error(err))
	} else {
		// Check which global announcements the player hasn't seen yet
		for _, globalAnnouncement := range globalAnnouncements {
			// Skip if the announcement hasn't started yet
			if globalAnnouncement.StartTime > time.Now().Unix() {
				continue
			}

			// Check if the player already has this announcement
			found := false
			for _, playerAnnouncement := range announcementBox.Announcements {
				if playerAnnouncement.AnnouncementId == int32(globalAnnouncement.Id) {
					found = true
					break
				}
			}

			// If the player doesn't have this announcement, add it
			if !found {
				announcement := &models.Announcement{
					Id:             announcementBox.GetMaxId() + 1,
					PlayerId:       playerId,
					AnnouncementId: int32(globalAnnouncement.Id),
					Type:           globalAnnouncement.Type,
					Title:          globalAnnouncement.Title,
					Content:        globalAnnouncement.Content,
					Status:         int32(pb.EAnnouncementStatus_EAnnouncementStatus_Unread),
					Priority:       globalAnnouncement.Priority,
					CreatedAt:      time.Now().Unix(),
					StartTime:      globalAnnouncement.StartTime,
					EndTime:        globalAnnouncement.EndTime,
					ShowInterval:   globalAnnouncement.ShowInterval,
					JumpTarget:     globalAnnouncement.JumpTarget,
				}

				announcementBox.Announcements[announcement.Id] = announcement
				announcementBox.SetMaxId(announcement.Id)

				// Save the new announcement to database
				dbAnnouncement := models.PlayerAnnouncement{
					Id:             announcement.Id,
					PlayerId:       announcement.PlayerId,
					AnnouncementId: announcement.AnnouncementId,
					Type:           announcement.Type,
					Title:          announcement.Title,
					Content:        announcement.Content,
					Status:         announcement.Status,
					Priority:       announcement.Priority,
					CreatedAt:      announcement.CreatedAt,
					StartTime:      announcement.StartTime,
					EndTime:        announcement.EndTime,
					ShowInterval:   announcement.ShowInterval,
					JumpTarget:     announcement.JumpTarget,
				}

				err := bootstrap.MysqlDb.Create(&dbAnnouncement).Error
				if err != nil {
					logger.Error("Failed to save global announcement to player",
						zap.Error(err),
						zap.Uint64("player_id", playerId),
						zap.Int32("announcement_id", announcement.AnnouncementId))
				}
			}
		}
	}

	p.SetAnnouncementBox(announcementBox)
}

// SavePlayerAnnouncements saves player announcements to database
func SavePlayerAnnouncements(announcementBox *models.PlayerAnnouncementBox) {
	if announcementBox == nil {
		return
	}

	// Get all announcement IDs to check which ones need to be deleted
	var existingIds []uint32
	err := bootstrap.MysqlDb.Model(&models.PlayerAnnouncement{}).
		Where("player_id = ? AND deleted_at = 0", announcementBox.PlayerId).
		Pluck("id", &existingIds).Error

	if err != nil {
		logger.Error("Failed to query existing announcement IDs",
			zap.Error(err),
			zap.Uint64("player_id", announcementBox.PlayerId))
		return
	}

	// Convert to map for easier lookup
	existingIdMap := make(map[uint32]bool)
	for _, id := range existingIds {
		existingIdMap[id] = true
	}

	// Process each announcement in memory
	for _, announcement := range announcementBox.Announcements {
		dbAnnouncement := models.PlayerAnnouncement{
			Id:             announcement.Id,
			PlayerId:       announcement.PlayerId,
			AnnouncementId: announcement.AnnouncementId,
			Type:           announcement.Type,
			Title:          announcement.Title,
			Content:        announcement.Content,
			Status:         announcement.Status,
			Priority:       announcement.Priority,
			CreatedAt:      announcement.CreatedAt,
			StartTime:      announcement.StartTime,
			EndTime:        announcement.EndTime,
			ShowInterval:   announcement.ShowInterval,
			JumpTarget:     announcement.JumpTarget,
		}

		// Check if announcement exists in database
		if existingIdMap[announcement.Id] {
			// Update existing announcement
			delete(existingIdMap, announcement.Id) // Remove from map to track deleted announcements

			err := bootstrap.MysqlDb.Model(&models.PlayerAnnouncement{}).
				Where("id = ?", announcement.Id).
				Updates(map[string]interface{}{
					"status": announcement.Status,
				}).Error

			if err != nil {
				logger.Error("Failed to update announcement",
					zap.Error(err),
					zap.Uint32("announcement_id", announcement.Id))
			}
		} else {
			// Insert new announcement
			err := bootstrap.MysqlDb.Create(&dbAnnouncement).Error
			if err != nil {
				logger.Error("Failed to create announcement",
					zap.Error(err),
					zap.Uint32("announcement_id", announcement.Id))
			}
		}
	}

	// Soft delete announcements that are no longer in memory
	for id := range existingIdMap {
		err := bootstrap.MysqlDb.Model(&models.PlayerAnnouncement{}).
			Where("id = ?", id).
			Update("deleted_at", time.Now().Unix()).Error

		if err != nil {
			logger.Error("Failed to soft delete announcement",
				zap.Error(err),
				zap.Uint32("announcement_id", id))
		}
	}
}

// CreateAnnouncement creates a new announcement
func CreateAnnouncement(announcementType pb.EAnnouncementType, title, content string, priority int32, startTime, endTime int64, showInterval int32, jumpTarget string) (*models.GlobalAnnouncement, error) {
	// Create a new global announcement
	announcement := &models.GlobalAnnouncement{
		Type:         int32(announcementType),
		Title:        title,
		Content:      content,
		Priority:     priority,
		CreatedAt:    time.Now().Unix(),
		StartTime:    startTime,
		EndTime:      endTime,
		ShowInterval: showInterval,
		JumpTarget:   jumpTarget,
	}

	// Save to database
	err := bootstrap.MysqlDb.Create(announcement).Error
	if err != nil {
		logger.Error("Failed to create global announcement",
			zap.Error(err),
			zap.String("title", title))
		return nil, err
	}

	return announcement, nil
}

// ReadAnnouncement marks an announcement as read
func ReadAnnouncement(p I.IPlayer, announcementId uint32) (*models.Announcement, error) {
	announcementBox := p.GetAnnouncementBox()
	if announcementBox == nil {
		return nil, errors.New("player announcement box not initialized")
	}

	announcement, ok := announcementBox.Announcements[announcementId]
	if !ok {
		return nil, errors.New("announcement not found")
	}

	// Only update status if it's unread
	if announcement.Status == int32(pb.EAnnouncementStatus_EAnnouncementStatus_Unread) {
		announcement.Status = int32(pb.EAnnouncementStatus_EAnnouncementStatus_Read)

		// Update in database
		err := bootstrap.MysqlDb.Model(&models.PlayerAnnouncement{}).
			Where("id = ?", announcementId).
			Update("status", announcement.Status).Error

		if err != nil {
			logger.Error("Failed to update announcement status",
				zap.Error(err),
				zap.Uint32("announcement_id", announcementId))
			return nil, err
		}
	}

	return announcement, nil
}

// GetAnnouncementList returns the list of announcements for a player
func GetAnnouncementList(p I.IPlayer, announcementType *pb.EAnnouncementType) []*models.Announcement {
	announcementBox := p.GetAnnouncementBox()
	if announcementBox == nil {
		return nil
	}

	// Convert map to slice
	announcements := make([]*models.Announcement, 0, len(announcementBox.Announcements))
	for _, announcement := range announcementBox.Announcements {
		// Filter by type if specified
		if announcementType != nil && announcement.Type != int32(*announcementType) {
			continue
		}

		// Skip expired announcements
		if announcement.EndTime > 0 && announcement.EndTime < time.Now().Unix() {
			continue
		}

		// Skip announcements that haven't started yet
		if announcement.StartTime > time.Now().Unix() {
			continue
		}

		announcements = append(announcements, announcement)
	}

	return announcements
}

// NotifyNewAnnouncement notifies the player of a new announcement
func NotifyNewAnnouncement(p I.IPlayer, announcement *models.Announcement) {
	// Convert announcement to protocol message
	announcementMsg := &pb.S2CAnnouncementNotify{
		Announcement: convertAnnouncementToProto(announcement),
	}

	// Send to player
	p.WriteMsg(announcementMsg)
}

// convertAnnouncementToProto converts an announcement to a protocol message
func convertAnnouncementToProto(announcement *models.Announcement) *pb.AnnouncementInfo {
	return &pb.AnnouncementInfo{
		Id:           proto.Uint32(announcement.Id),
		Type:         pb.EAnnouncementType(announcement.Type).Enum(),
		Title:        proto.String(announcement.Title),
		Content:      proto.String(announcement.Content),
		Status:       proto.Int32(announcement.Status),
		Priority:     proto.Int32(announcement.Priority),
		CreatedAt:    proto.Int64(announcement.CreatedAt),
		StartTime:    proto.Int64(announcement.StartTime),
		EndTime:      proto.Int64(announcement.EndTime),
		ShowInterval: proto.Int32(announcement.ShowInterval),
		JumpTarget:   proto.String(announcement.JumpTarget),
	}
}
