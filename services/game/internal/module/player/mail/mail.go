package mail

import (
	"errors"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/game_config"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/services/game/internal/module/player/assets"
	"kairo_paradise_server/services/pb"
	"time"
)

// LoadPlayerMails loads player mails from database
func LoadPlayerMails(p I.IPlayer) {
	playerId := p.GetPlayerId()

	// Create a new mailbox for the player
	mailBox := models.NewPlayerMailBox(playerId)

	var dbMails []models.PlayerMail
	err := bootstrap.MysqlDb.Model(&models.PlayerMail{}).
		Where("player_id = ? AND deleted_at = 0", playerId).
		Find(&dbMails).Error

	if err != nil && !errors.As(err, &gorm.ErrRecordNotFound) {
		logger.Error("Failed to load player mails", zap.Error(err), zap.Uint64("player_id", playerId))
		p.SetMailBox(mailBox)
		return
	}

	// Convert database mails to memory mails
	for _, dbMail := range dbMails {
		// Skip expired mails
		if dbMail.ExpireAt > 0 && dbMail.ExpireAt < time.Now().Unix() {
			continue
		}

		mail := &models.Mail{
			Id:        dbMail.Id,
			PlayerId:  dbMail.PlayerId,
			MailId:    dbMail.MailId,
			Title:     dbMail.Title,
			Content:   dbMail.Content,
			Sender:    dbMail.Sender,
			Status:    dbMail.Status,
			CreatedAt: dbMail.CreatedAt,
			ExpireAt:  dbMail.ExpireAt,
		}

		mailBox.Mails[mail.Id] = mail
	}

	// Set the max mail ID
	mailBox.SetMaxId(mailBox.CalculateMaxId())

	// Set the mailbox to the player
	p.SetMailBox(mailBox)
}

// SavePlayerMails saves player mails to database
func SavePlayerMails(mailBox *models.PlayerMailBox) {
	if mailBox == nil {
		return
	}

	// Get all mail IDs to check which ones need to be deleted
	var existingIds []uint64
	err := bootstrap.MysqlDb.Model(&models.PlayerMail{}).
		Where("player_id = ? AND deleted_at = 0", mailBox.PlayerId).
		Pluck("id", &existingIds).Error

	if err != nil {
		logger.Error("Failed to query existing mail IDs", zap.Error(err), zap.Uint64("player_id", mailBox.PlayerId))
		return
	}

	// Convert to map for easier lookup
	existingIdMap := make(map[uint64]bool)
	for _, id := range existingIds {
		existingIdMap[id] = true
	}

	// Process each mail in memory
	for _, mail := range mailBox.Mails {
		dbMail := models.PlayerMail{
			Id:        mail.Id,
			PlayerId:  mail.PlayerId,
			MailId:    mail.MailId,
			Title:     mail.Title,
			Content:   mail.Content,
			Sender:    mail.Sender,
			Status:    mail.Status,
			CreatedAt: mail.CreatedAt,
			ExpireAt:  mail.ExpireAt,
		}

		// Check if mail exists in database
		if existingIdMap[mail.Id] {
			// Update existing mail
			delete(existingIdMap, mail.Id) // Remove from map to track deleted mails

			err := bootstrap.MysqlDb.Model(&models.PlayerMail{}).
				Where("id = ?", mail.Id).
				Updates(map[string]interface{}{
					"status":      mail.Status,
					"attachments": dbMail.Attachments,
				}).Error

			if err != nil {
				logger.Error("Failed to update mail", zap.Error(err), zap.Uint64("mail_id", mail.Id))
			}
		} else {
			// Insert new mail
			err := bootstrap.MysqlDb.Create(&dbMail).Error
			if err != nil {
				logger.Error("Failed to create mail", zap.Error(err), zap.Uint64("mail_id", mail.Id))
			}
		}
	}

	// Soft delete mails that exist in database but not in memory
	if len(existingIdMap) > 0 {
		var idsToDelete []uint64
		for id := range existingIdMap {
			idsToDelete = append(idsToDelete, id)
		}

		err := bootstrap.MysqlDb.Model(&models.PlayerMail{}).
			Where("id IN ?", idsToDelete).
			Update("deleted_at", time.Now().Unix()).Error

		if err != nil {
			logger.Error("Failed to soft delete mails", zap.Error(err), zap.Any("mail_ids", idsToDelete))
		}
	}
}

// SendMail sends a mail to a player
func SendMail(p I.IPlayer, mailId int32, attachments []*pb.ItemData, expireSeconds int64) (*models.Mail, error) {
	mailBox := p.GetMailBox()
	if mailBox == nil {
		return nil, errors.New("player mailbox not initialized")
	}

	mailConfig := game_config.MailConfig.Item(mailId)
	if mailConfig == nil || mailConfig.Id == nil {
		return nil, errors.New("mail template not found")
	}

	mail := &models.Mail{
		Id:        mailBox.GetMaxId() + 1,
		PlayerId:  p.GetPlayerId(),
		MailId:    mailId,
		Title:     *mailConfig.Title,
		Content:   *mailConfig.Content,
		Sender:    *mailConfig.Sender,
		Status:    int32(pb.EMailStatus_EMailStatus_Unread),
		CreatedAt: time.Now().Unix(),
	}

	if expireSeconds > 0 {
		mail.ExpireAt = time.Now().Unix() + expireSeconds
	}

	if len(attachments) > 0 {
		mail.Attachments = convertItemData(attachments)
	} else if mailConfig.Attachments != nil && len(mailConfig.Attachments) > 0 {
		for _, item := range mailConfig.Attachments {
			if item.Id != nil && item.Count != nil {
				itemConfig := game_config.ItemConfig.Item(*item.Id)
				if itemConfig == nil || itemConfig.Id == nil {
					continue
				}
				mail.Attachments = append(mail.Attachments, models.ItemData{
					Id:    *item.Id,
					Count: *item.Count,
				})
			}
		}
	}

	// Add mail to mailbox
	mailBox.Mails[mail.Id] = mail
	mailBox.SetMaxId(mail.Id)

	// Save to database
	dbMail := models.PlayerMail{
		Id:          mail.Id,
		PlayerId:    mail.PlayerId,
		MailId:      mail.MailId,
		Title:       mail.Title,
		Content:     mail.Content,
		Sender:      mail.Sender,
		Status:      mail.Status,
		CreatedAt:   mail.CreatedAt,
		ExpireAt:    mail.ExpireAt,
		Attachments: mail.Attachments,
	}

	// Save to database
	err := bootstrap.MysqlDb.Create(&dbMail).Error
	if err != nil {
		logger.Error("Failed to save mail to database", zap.Error(err), zap.Uint64("mail_id", mail.Id))
		return nil, err
	}

	// Notify player of new mail
	NotifyNewMail(p, mail)

	return mail, nil
}

// SendCustomMail sends a custom mail to a player
func SendCustomMail(p I.IPlayer, title, content, sender string, attachments []*pb.ItemData, expireSeconds int64) (*models.Mail, error) {
	mailBox := p.GetMailBox()
	if mailBox == nil {
		return nil, errors.New("player mailbox not initialized")
	}

	// Create new mail
	mail := &models.Mail{
		Id:        mailBox.GetMaxId() + 1,
		PlayerId:  p.GetPlayerId(),
		MailId:    0, // Custom mail has no template ID
		Title:     title,
		Content:   content,
		Sender:    sender,
		Status:    int32(pb.EMailStatus_EMailStatus_Unread),
		CreatedAt: time.Now().Unix(),
	}

	// Set expiration time if specified
	if expireSeconds > 0 {
		mail.ExpireAt = time.Now().Unix() + expireSeconds
	}

	// Add attachments if any
	if len(attachments) > 0 {
		mail.Attachments = convertItemData(attachments)
	}

	// Add mail to mailbox
	mailBox.Mails[mail.Id] = mail
	mailBox.SetMaxId(mail.Id)

	// Save to database
	dbMail := models.PlayerMail{
		Id:          mail.Id,
		PlayerId:    mail.PlayerId,
		MailId:      mail.MailId,
		Title:       mail.Title,
		Content:     mail.Content,
		Sender:      mail.Sender,
		Status:      mail.Status,
		CreatedAt:   mail.CreatedAt,
		ExpireAt:    mail.ExpireAt,
		Attachments: mail.Attachments,
	}

	err := bootstrap.MysqlDb.Create(&dbMail).Error
	if err != nil {
		logger.Error("Failed to save mail to database", zap.Error(err), zap.Uint64("mail_id", mail.Id))
		return nil, err
	}

	NotifyNewMail(p, mail)

	return mail, nil
}

// ReadMail marks a mail as read
func ReadMail(p I.IPlayer, mailId uint64) (*models.Mail, error) {
	mailBox := p.GetMailBox()
	if mailBox == nil {
		return nil, errors.New("player mailbox not initialized")
	}

	mail, ok := mailBox.Mails[mailId]
	if !ok {
		return nil, errors.New("mail not found")
	}

	if mail.Status == int32(pb.EMailStatus_EMailStatus_Unread) {
		mail.Status = int32(pb.EMailStatus_EMailStatus_Read)

		err := bootstrap.MysqlDb.Model(&models.PlayerMail{}).
			Where("id = ?", mailId).
			Update("status", mail.Status).Error

		if err != nil {
			logger.Error("Failed to update mail status",
				zap.Error(err),
				zap.Uint64("mail_id", mailId))
			return nil, err
		}
	}

	return mail, nil
}

// ClaimMailAttachments claims the attachments from a mail
func ClaimMailAttachments(p I.IPlayer, mailId uint64) error {
	mailBox := p.GetMailBox()
	if mailBox == nil {
		return errors.New("player mailbox not initialized")
	}

	mail, ok := mailBox.Mails[mailId]
	if !ok {
		return errors.New("mail not found")
	}

	// Check if mail has already been claimed
	if mail.Status == int32(pb.EMailStatus_EMailStatus_Gain) {
		return errors.New("mail attachments already claimed")
	}

	// Check if mail has attachments
	if len(mail.Attachments) == 0 {
		return errors.New("mail has no attachments")
	}

	// Add items to player's inventory
	for _, item := range mail.Attachments {
		if item.Id != 0 && item.Count != 0 {
			assets.AddItemApi(p, item.Id, item.Count, pb.EResourceAddType_EResourceAddType_SystemMail.Enum())
		}
	}

	// Mark mail as claimed
	mail.Status = int32(pb.EMailStatus_EMailStatus_Gain)
	mail.Attachments = nil // Clear attachments after claiming

	// Update in database
	err := bootstrap.MysqlDb.Model(&models.PlayerMail{}).
		Where("id = ?", mailId).
		Updates(map[string]interface{}{
			"status":      mail.Status,
			"attachments": []byte{}, // Clear attachments
		}).Error

	if err != nil {
		logger.Error("Failed to update mail after claiming attachments",
			zap.Error(err),
			zap.Uint64("mail_id", mailId))
		return err
	}

	return nil
}

// DeleteMail deletes a mail
func DeleteMail(p I.IPlayer, mailId uint64) error {
	mailBox := p.GetMailBox()
	if mailBox == nil {
		return errors.New("player mailbox not initialized")
	}

	_, ok := mailBox.Mails[mailId]
	if !ok {
		return errors.New("mail not found")
	}

	// Remove from memory
	delete(mailBox.Mails, mailId)

	// Soft delete in database
	err := bootstrap.MysqlDb.Model(&models.PlayerMail{}).
		Where("id = ?", mailId).
		Update("deleted_at", time.Now().Unix()).Error

	if err != nil {
		logger.Error("Failed to delete mail", zap.Error(err), zap.Uint64("mail_id", mailId))
		return err
	}

	return nil
}

// GetMailList returns the list of mails for a player
func GetMailList(p I.IPlayer) []*models.Mail {
	mailBox := p.GetMailBox()
	if mailBox == nil {
		return nil
	}

	// Convert map to slice
	mails := make([]*models.Mail, 0, len(mailBox.Mails))
	for _, mail := range mailBox.Mails {
		mails = append(mails, mail)
	}

	return mails
}

// NotifyNewMail notifies the player of a new mail
func NotifyNewMail(p I.IPlayer, mail *models.Mail) {
	// Convert mail to protocol message
	mailMsg := &pb.S2CMailNotify{
		Mail: convertMailToProto(mail),
	}

	// Send to player
	p.WriteMsg(mailMsg)
}

// convertMailToProto converts a mail model to a protocol message
func convertMailToProto(mail *models.Mail) *pb.MailInfo {
	protoMail := &pb.MailInfo{
		Id:        proto.Uint64(mail.Id),
		Title:     proto.String(mail.Title),
		Content:   proto.String(mail.Content),
		Sender:    proto.String(mail.Sender),
		Status:    proto.Int32(mail.Status),
		CreatedAt: proto.Int64(mail.CreatedAt),
	}
	for _, item := range mail.Attachments {
		protoMail.Attachments = append(protoMail.Attachments, &pb.ItemData{
			Id:    proto.Int32(item.Id),
			Count: proto.Int32(item.Count),
		})
	}
	return protoMail
}

func convertItemData(attachments []*pb.ItemData) []models.ItemData {
	var itemList []models.ItemData
	for _, item := range attachments {
		itemList = append(itemList, models.ItemData{
			Id:    *item.Id,
			Count: *item.Count,
		})
	}
	return itemList
}
