package player

import (
	"errors"
	"gorm.io/gorm"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/models"
	"time"
)

func GetPlayerInfo(userId uint32, serverId int) (*models.Player, error) {
	var info models.Player
	err := bootstrap.MysqlDb.Model(models.Player{}).Where("user_id = ? and server_id = ?", userId, serverId).First(&info).Error
	if err != nil && !errors.As(err, &gorm.ErrRecordNotFound) {
		return nil, err
	}
	if info.Id > 0 {
		return &info, nil
	}
	info.UserId = userId
	info.ServerId = serverId
	info.Level = 1
	info.Icon = 1
	info.CreatedAt = time.Now().Unix()

	err = bootstrap.MysqlDb.Create(&info).Error
	return &info, err
}

func UpdatePlayerInfo(playerId uint64, updates models.Player) error {
	err := bootstrap.MysqlDb.Model(&models.Player{}).Where("id = ?", playerId).Updates(updates).Error
	return err
}
