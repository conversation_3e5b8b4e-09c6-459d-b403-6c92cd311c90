package models

import (
	"gorm.io/gorm"
	"time"
)

// gorm 数据库模型

type UserInfo struct {
	Id          uint32 `gorm:"primaryKey;autoIncrement"`
	State       uint   `gorm:"type:tinyint unsigned;not null;default:0"`
	Status      uint   `gorm:"type:tinyint unsigned;not null;default:0"`
	Age         uint   `gorm:"type:int unsigned; not null; default:0"`
	Auth        uint   `gorm:"type:int unsigned; not null; default:2"`
	Birthday    string `gorm:"type:string; not null"`
	UserType    uint   `gorm:"column:user_type;type:tinyint unsigned;not null;default:0"`
	RegisterTs  int64  `gorm:"column:register_ts;type:int unsigned;not null;default:0"`
	LastLoginTs int64  `gorm:"column:last_login_ts;type:int unsigned;not null;default:0"`
	LastLoginIp string `gorm:"column:last_login_ip;type:varchar(128);not null"`
}

func (u *UserInfo) TableName() string {
	return "t_user_info"
}

func (u *UserInfo) BeforeCreate(tx *gorm.DB) (err error) {
	u.RegisterTs = time.Now().Unix()
	return
}

type Oauth struct {
	UserId     uint32 `gorm:"primaryKey"`
	Account    string `gorm:"type:varchar(100);not null"`
	ChannelNo  string `gorm:"column:channel_no;type:varchar(30);not null"`
	Channel    string `gorm:"type:varchar(30);not null"`
	UserType   uint   `gorm:"-"`
	IsRegister bool   `gorm:"-"`
}

func (u *Oauth) TableName() string {
	return "t_oauth"
}

type Account struct {
	UserId     uint32 `gorm:"primaryKey"`
	Account    string `gorm:"type:varchar(30);not null"`
	Password   string `gorm:"type:varchar(64);not null"`
	Salt       string `gorm:"type:varchar(32);not null"`
	UserType   uint   `gorm:"-"`
	IsRegister bool   `gorm:"-"`
}

func (u *Account) TableName() string {
	return "t_account"
}

type Player struct {
	Id         uint64 `gorm:"primaryKey;autoIncrement"`
	UserId     uint32 `gorm:"column:user_id"`
	ServerId   int    `gorm:"column:server_id;type:int unsigned;not null;default:0"`
	Nick       string `gorm:"column:nick;"`
	Icon       int32  `gorm:"column:icon;type:int unsigned;not null;default:1"`
	Gender     int32  `gorm:"column:gender;type:int unsigned;not null;default:0"`
	Level      int32  `gorm:"column:level;type:int unsigned;not null;default:1"`
	Prosperity int32  `gorm:"column:prosperity;type:int unsigned;not null;default:0"`
	CreatedAt  int64  `gorm:"column:created_at;type:int unsigned;not null;default:0"`
}

func (u *Player) TableName() string {
	return "t_player"
}

type PlayerAsset struct {
	PlayerId     uint64 `gorm:"primaryKey;"`
	Coin         int32  `gorm:"column:coin;type:int;not null;default:0"`
	MaxItemId    int32  `gorm:"column:max_item_id;type:int;not null;default:0"`
	ItemList     []byte `gorm:"column:item_list;type:blob"`
	ItemListJson string `gorm:"column:item_list_json"`
	Scene        []byte `gorm:"column:scene;type:blob"`
}

func (u *PlayerAsset) TableName() string {
	return "t_player_asset"
}

type PlayerTime struct {
	PlayerId           uint64    `gorm:"primaryKey;"`
	CreateTime         int64     `gorm:"column:create_time;type:bigint;not null;default:0"`
	CreateLocalTime    time.Time `gorm:"column:create_local_time;type:datetime(3);not null"`
	LoginTime          int64     `gorm:"column:login_time;type:bigint;not null;default:0"`
	LogoutTime         int64     `gorm:"column:logout_time;type:bigint;not null;default:0"`
	DailyResetTime     int64     `gorm:"column:daily_reset_time;type:bigint;not null;default:0"`
	ZoneDailyResetTime int64     `gorm:"column:zone_daily_reset_time;type:bigint;not null;default:0"`
	StayFlag           int       `gorm:"column:stay_flag;type:int;not null;default:0"`
}

func (u *PlayerTime) TableName() string {
	return "t_player_time"
}
