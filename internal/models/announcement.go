package models

// PlayerAnnouncement represents an announcement in the database
type PlayerAnnouncement struct {
	Id             uint32 `gorm:"primaryKey;autoIncrement"`
	PlayerId       uint64 `gorm:"column:player_id;index"`
	AnnouncementId int32  `gorm:"column:announcement_id"`                                   // Announcement template ID from config
	Type           int32  `gorm:"column:type;type:int;not null"`                            // Announcement type
	Title          string `gorm:"column:title;type:varchar(128);not null"`                  // Announcement title
	Content        string `gorm:"column:content;type:text;not null"`                        // Announcement content
	Status         int32  `gorm:"column:status;type:int;not null;default:1"`                // Announcement status: 1=unread, 2=read
	Priority       int32  `gorm:"column:priority;type:int;not null;default:0"`              // Priority for display order
	CreatedAt      int64  `gorm:"column:created_at;type:bigint;not null"`                   // Creation timestamp
	StartTime      int64  `gorm:"column:start_time;type:bigint;not null"`                   // Start timestamp
	EndTime        int64  `gorm:"column:end_time;type:bigint;not null"`                     // End timestamp
	ShowInterval   int32  `gorm:"column:show_interval;type:int;not null;default:0"`         // Show interval in seconds
	JumpTarget     string `gorm:"column:jump_target;type:varchar(128);not null;default:''"` // Jump target
	DeletedAt      int64  `gorm:"column:deleted_at;type:bigint;not null;default:0"`         // Soft delete timestamp
}

func (a *PlayerAnnouncement) TableName() string {
	return "t_player_announcement"
}

// Announcement represents an announcement in memory
type Announcement struct {
	Id             uint32
	PlayerId       uint64
	AnnouncementId int32
	Type           int32
	Title          string
	Content        string
	Status         int32
	Priority       int32
	CreatedAt      int64
	StartTime      int64
	EndTime        int64
	ShowInterval   int32
	JumpTarget     string
}

// PlayerAnnouncementBox represents a player's announcement box in memory
type PlayerAnnouncementBox struct {
	PlayerId      uint64
	Announcements map[uint32]*Announcement // Map of announcement ID to announcement
	maxId         uint32
}

func (a *PlayerAnnouncementBox) GetMaxId() uint32 {
	return a.maxId
}

func (a *PlayerAnnouncementBox) SetMaxId(id uint32) {
	a.maxId = id
}

func (a *PlayerAnnouncementBox) CalculateMaxId() uint32 {
	maxID := uint32(0)
	for id := range a.Announcements {
		if id > maxID {
			maxID = id
		}
	}
	return maxID
}

// NewPlayerAnnouncementBox creates a new player announcement box
func NewPlayerAnnouncementBox(playerId uint64) *PlayerAnnouncementBox {
	return &PlayerAnnouncementBox{
		PlayerId:      playerId,
		Announcements: make(map[uint32]*Announcement),
	}
}

// GlobalAnnouncement represents a global announcement in the database
type GlobalAnnouncement struct {
	Id           uint32 `gorm:"primaryKey;autoIncrement"`
	Type         int32  `gorm:"column:type;type:int;not null"`                            // Announcement type
	Title        string `gorm:"column:title;type:varchar(128);not null"`                  // Announcement title
	Content      string `gorm:"column:content;type:text;not null"`                        // Announcement content
	Priority     int32  `gorm:"column:priority;type:int;not null;default:0"`              // Priority for display order
	CreatedAt    int64  `gorm:"column:created_at;type:bigint;not null"`                   // Creation timestamp
	StartTime    int64  `gorm:"column:start_time;type:bigint;not null"`                   // Start timestamp
	EndTime      int64  `gorm:"column:end_time;type:bigint;not null"`                     // End timestamp
	ShowInterval int32  `gorm:"column:show_interval;type:int;not null;default:0"`         // Show interval in seconds
	JumpTarget   string `gorm:"column:jump_target;type:varchar(128);not null;default:''"` // Jump target
	DeletedAt    int64  `gorm:"column:deleted_at;type:bigint;not null;default:0"`         // Soft delete timestamp
}

func (a *GlobalAnnouncement) TableName() string {
	return "t_global_announcement"
}
