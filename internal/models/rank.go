package models

import "time"

type Rank struct {
	RankType    int32     `gorm:"primaryKey;column:rank_type;type:int unsigned not null default 0" json:"rank_type"`
	Ranking     int32     `gorm:"primaryKey;column:ranking;type:int unsigned not null default 0" json:"ranking"`
	PlayerID    uint64    `gorm:"column:player_id;type:int unsigned not null default 0" json:"player_id"`
	Score       int32     `gorm:"column:score;type:int unsigned not null default 0" json:"score"`
	UpdatedAt   int64     `gorm:"column:updated_at;type:int unsigned not null default 0" json:"updated_at"`
	UpdatedTime time.Time `gorm:"column:updated_time;type:datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_time"`
}

func (m *Rank) TableName() string {
	return "t_rank"
}

// RankSnapshot 排行榜快照表模型（用于历史数据保存）
type RankSnapshot struct {
	ID           uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	RankType     int32     `gorm:"column:rank_type;type:int;not null;index:idx_snapshot_type_time" json:"rank_type"`            // 排行榜类型
	SnapshotTime int64     `gorm:"column:snapshot_time;type:bigint;not null;index:idx_snapshot_type_time" json:"snapshot_time"` // 快照时间
	TotalCount   int64     `gorm:"column:total_count;type:bigint;not null;default:0" json:"total_count"`                        // 总数量
	DataVersion  int32     `gorm:"column:data_version;type:int;not null;default:1" json:"data_version"`                         // 数据版本
	Snapshot     []byte    `gorm:"column:snapshot;type:blob"`                                                                   // 快照数据
	CreatedAt    time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
}

// TableName 指定表名
func (r *RankSnapshot) TableName() string {
	return "t_rank_snapshot"
}
