package models

import "kairo_paradise_server/services/pb"

type Assets struct {
	PlayerId uint64
	Coin     int32
	ItemList map[int32][]*pb.BagItem // map[itemId][]*pb.ItemData
	maxId    int32
}

func (c *Assets) GetMaxId() int32 {
	return c.maxId
}

func (c *Assets) SetMaxId(id int32) {
	c.maxId = id
}

func (c *Assets) IncrMaxId() int32 {
	c.maxId++
	return c.maxId
}

func (c *Assets) CalculateMaxId() int32 {
	maxID := int32(0)
	for _, v := range c.ItemList {
		for _, item := range v {
			if *item.Id > maxID {
				maxID = *item.Id
			}
		}
	}
	return maxID
}

type Scene struct {
	PlayerId  uint64
	BuildList []*pb.BuildData
}
