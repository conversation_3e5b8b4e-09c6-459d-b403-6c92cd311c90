package I

import "kairo_paradise_server/internal/models"

type IPlayer interface {
	GetPlayerId() uint64
	GetUserId() uint32

	GetUserInfo() *models.UserInfo
	GetPlayerInfo() *models.Player

	GetAssets() *models.Assets
	SetAssets(assets *models.Assets)

	GetScene() *models.Scene
	SetScene(scene *models.Scene)

	GetMailBox() *models.PlayerMailBox
	SetMailBox(mailBox *models.PlayerMailBox)

	GetAnnouncementBox() *models.PlayerAnnouncementBox
	SetAnnouncementBox(announcementBox *models.PlayerAnnouncementBox)

	IsRobot() bool

	IsRegister() bool

	SetServices(serviceStr string)
	SetUserInfo(userInfo *models.UserInfo)
	SetInfo(info *models.Player)
	SetIsReconnect(isReconnect bool)

	OnAfterLoad()
	OnLoginSuccess()
	OnDisconnect()
	OnDestroy()
	CleanupCancel()
	SetCleanupCancel(cancel func())

	WriteMsg(message interface{})
	WritePump()

	Ticker()
}
