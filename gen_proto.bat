setlocal enabledelayedexpansion
@echo off
echo start proto
set Dir=%~dp0
set LocalProtoFileDir=%Dir%proto
set ProtoFileDir=..\kairo-paradise-resource-new\ProtobufToolNew\proto

echo %GoOut%
echo %GrpcOut%
echo %ProtoFileDir%

REM Generate Go code from proto files
protoc.exe --go_out=paths=source_relative:services/pb --go-grpc_out=paths=source_relative:services/pb  --proto_path=%ProtoFileDir% %ProtoFileDir%\*.proto
protoc.exe --go_out=paths=source_relative:services/pb --go-grpc_out=paths=source_relative:services/pb  --proto_path=%LocalProtoFileDir% %LocalProtoFileDir%\*.proto

py.exe .\services\pb\export_exe\export_protobuf.py %ProtoFileDir% .\services\pb\msg\

echo finish proto generation

echo.

PAUSE
